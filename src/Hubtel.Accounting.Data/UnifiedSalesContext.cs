using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Hubtel.Accounting.Data.EntityModels;

namespace Hubtel.Accounting.Data
{
    public class UnifiedSalesContext : DbContext
    {
        public UnifiedSalesContext(DbContextOptions<UnifiedSalesContext> dbContextOptions) : base(dbContextOptions)
        {
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var config = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", true, true)
                .Build();
            optionsBuilder.UseNpgsql(config.GetSection("DatabaseSettings")["SalesReaderConnection"]);

        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            //modelBuilder.Entity<PaymentAggregate>()
            //    .HasIndex(o => new {
            //        o.FineractSavingsAccountId,
            //        o.MobileMoneyNetwork,
            //        o.CardProcessor,
            //        o.CardTransactionMode,
            //        o.ChargeCustomer,
            //        o.HourTimestamp
            //    }).IsUnique();

            //foreach (var property in modelBuilder.Model.GetEntityTypes()
            //    .SelectMany(t => t.GetProperties())
            //    .Where(p => p.ClrType == typeof(decimal)))
            //{
            //    property.Relational().ColumnType = "decimal(20, 4)";
            //}
        }

        
        public DbSet<NewPayment> Payments { get; set; }
    }
}

// <auto-generated />
using System;
using Hubtel.Accounting.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

namespace Hubtel.Accounting.Data.Migrations
{
    [DbContext(typeof(AccountingDbContext))]
    [Migration("20190501204305_InitialMigration")]
    partial class InitialMigration
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn)
                .HasAnnotation("ProductVersion", "2.2.4-servicing-10062")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            modelBuilder.Entity("Hubtel.Accounting.Data.EntityModels.LastQueryPoint", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd();

                    b.Property<DateTime>("CreatedAt");

                    b.Property<string>("PaymentId");

                    b.HasKey("Id");

                    b.ToTable("LastQueryPoints");
                });

            modelBuilder.Entity("Hubtel.Accounting.Data.EntityModels.PaymentAggregate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd();

                    b.Property<decimal>("AmountPaid")
                        .HasColumnType("decimal(20, 4)");

                    b.Property<string>("CardProcessor");

                    b.Property<string>("CardTransactionMode");

                    b.Property<bool?>("ChargeCustomer");

                    b.Property<decimal>("Fee")
                        .HasColumnType("decimal(20, 4)");

                    b.Property<long>("FineractSavingsAccountId");

                    b.Property<DateTime>("HourTimestamp");

                    b.Property<string>("MobileMoneyNetwork");

                    b.HasKey("Id");

                    b.HasIndex("FineractSavingsAccountId", "MobileMoneyNetwork", "CardProcessor", "CardTransactionMode", "ChargeCustomer", "HourTimestamp")
                        .IsUnique();

                    b.ToTable("PaymentAggregates");
                });
#pragma warning restore 612, 618
        }
    }
}

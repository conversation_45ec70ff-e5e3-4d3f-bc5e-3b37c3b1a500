using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Hubtel.Accounting.Data.Migrations
{
    public partial class LastPaymentTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "LastPaymentQuery",
                columns: table => new
                {
                    Id = table.Column<DateTime>(nullable: false),
                    CreatedAt = table.Column<DateTime>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LastPaymentQuery", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LastPaymentQuery");
        }
    }
}

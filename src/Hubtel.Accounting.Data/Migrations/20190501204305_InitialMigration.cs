using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

namespace Hubtel.Accounting.Data.Migrations
{
    public partial class InitialMigration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "LastQueryPoints",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn),
                    PaymentId = table.Column<string>(nullable: true),
                    CreatedAt = table.Column<DateTime>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LastQueryPoints", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PaymentAggregates",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.SerialColumn),
                    FineractSavingsAccountId = table.Column<long>(nullable: false),
                    ChargeCustomer = table.Column<bool>(nullable: true),
                    CardTransactionMode = table.Column<string>(nullable: true),
                    CardProcessor = table.Column<string>(nullable: true),
                    MobileMoneyNetwork = table.Column<string>(nullable: true),
                    Fee = table.Column<decimal>(type: "decimal(20, 4)", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "decimal(20, 4)", nullable: false),
                    HourTimestamp = table.Column<DateTime>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PaymentAggregates", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PaymentAggregates_FineractSavingsAccountId_MobileMoneyNetwo~",
                table: "PaymentAggregates",
                columns: new[] { "FineractSavingsAccountId", "MobileMoneyNetwork", "CardProcessor", "CardTransactionMode", "ChargeCustomer", "HourTimestamp" },
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LastQueryPoints");

            migrationBuilder.DropTable(
                name: "PaymentAggregates");
        }
    }
}

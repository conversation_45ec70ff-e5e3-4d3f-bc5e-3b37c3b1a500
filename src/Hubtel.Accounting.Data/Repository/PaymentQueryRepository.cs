using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hubtel.Accounting.Data.EntityModels;
using Microsoft.EntityFrameworkCore;

namespace Hubtel.Accounting.Data.Repository
{
    public class EfPaymentQueryRepository : IPaymentQueryRepository
    {
        private readonly UnifiedSalesContext _ctx;
        public EfPaymentQueryRepository(UnifiedSalesContext ctx)
        {
            _ctx = ctx;
        }

        public async Task<List<NewPayment>> GetPaymentsAsync(DateTime start, DateTime end)
        {
            return await _ctx.Payments
                .Where(x => x.CreatedAt >= start
                            && x.CreatedAt < end
                            && x.IsSuccessful 
                            && x.PaymentType.ToLower() != "cash"
                            && x.PaymentType.ToLower() != "bankpay")
                .OrderBy(x => x.CreatedAt)
                .AsNoTracking()
                .ToListAsync();
        }
    }

}
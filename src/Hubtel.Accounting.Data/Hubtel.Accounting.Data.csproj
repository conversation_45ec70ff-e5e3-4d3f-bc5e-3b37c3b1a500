<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>

        <TargetFramework>netcoreapp3.1</TargetFramework>
        <LangVersion>7.1</LangVersion>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="3.1.10" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="3.1.10">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>

        <PackageReference Include="Npgsql" Version="4.1.6" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="3.1.4" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="3.1.10" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="3.1.10" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="3.1.10" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="3.1.10" />

    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>
</Project>

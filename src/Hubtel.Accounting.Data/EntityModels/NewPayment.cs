using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Hubtel.Accounting.Data.EntityModels
{
    [Table("Payments")]
    public class NewPayment
    {

        public string Id { get; set; }

        public string BusinessId { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public string CreatedBy { get; set; }

        public string UpdatedBy { get; set; }
        public string PaymentType { get; set; }
        public string OrderId { get; set; }
       
        public string MomoPhoneNumber { get; set; }
       
        public string MomoChannel { get; set; }
       
        public string MomoToken { get; set; }
        
        public string TransactionId { get; set; } // from merchant account
       
        public string ExternalTransactionId { get; set; }// from telco to merchant account
       
        public decimal AmountAfterCharges { get; set; }
        
        public decimal Charges { get; set; }
      
        public bool ChargeCustomer { get; set; }
       
        public decimal AmountPaid { get; set; }

        public string BusinessName { get; set; }
        public DateTime PaymentDate { get; set; }
       
        public string Note { get; set; }
      
        public string Description { get; set; }
      
        public string PosDeviceId { get; set; }
       
        public string PosDeviceType { get; set; }
       
        public string EmployeeId { get; set; }
       
        public string EmployeeName { get; set; }
       
        public string CustomerMobileNumber { get; set; }
       
        public string CustomerName { get; set; }
       
        public string BranchId { get; set; }
       
        public string BranchName { get; set; }
       
        public bool IsRefund { get; set; }
       
        public bool IsSuccessful { get; set; }
      
        public string ReceiptNumber { get; set; } //this is autogenerated increment for organization for successful payments
       
        public string Location { get; set; }
       
        public string Currency { get; set; }
     
        public string Scheme { get; set; }
       
        public string Card { get; set; }
       
        public string Tid { get; set; }
      
        public string Authorization { get; set; }
     
        public string Mid { get; set; }
       
        public string CardTransactionId { get; set; }
       
        public decimal AmountTendered { get; set; }
      
        public decimal Balance { get; set; }
      
        public string ClientReference { get; set; }
      
        public string ProviderDescription { get; set; }
       
        public string StatusCode { get; set; }

        public int FineractSavingsAccountId { get; set; }

        public string CardTransactionMode { get; set; }

        public string CardProcessor { get; set; }

        public decimal DeliveryFee { get; set; }

        public bool? IsSyncedToFineract { get; set; }

        public string PaymentProcessor { get; set; }
        public decimal ElevyAmount { get; set; }
        public decimal Tips { get; set; }
        public decimal AmountPlusTips { get; set; }
        public decimal TipSettlement { get; set; }
        public bool HasTip {get;set;}
    }
}

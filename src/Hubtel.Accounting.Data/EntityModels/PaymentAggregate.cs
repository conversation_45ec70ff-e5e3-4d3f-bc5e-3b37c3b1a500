using System;
using System.Collections.Generic;
using System.Text;

namespace Hubtel.Accounting.Data.EntityModels
{
    public class PaymentAggregate
    {
        public int Id { get; set; }
        public long FineractSavingsAccountId { get; set; }
        public bool? ChargeCustomer { get; set; }
        public string CardTransactionMode { get; set; }
        public string CardProcessor { get; set; }
        public string MobileMoneyNetwork { get; set; }
        public decimal Fee { get; set; }
        public decimal AmountPaid { get; set; }
        public DateTime HourTimestamp { get; set; }
    }

}

using Hubtel.Accounting.Data.EntityModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Linq;
using System.Text;

namespace Hubtel.Accounting.Data
{
    public class AccountingDbContext : DbContext
    {
        public AccountingDbContext()
        {

        }
        public AccountingDbContext(DbContextOptions<AccountingDbContext> dbContextOptions) : base(dbContextOptions)
        {
        }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var config = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", true, true)
                .Build();
            optionsBuilder.UseNpgsql(config.GetSection("DatabaseSettings")["ConnectionString"]);

        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<PaymentAggregate>()
                .HasIndex(o => new { o.FineractSavingsAccountId, o.MobileMoneyNetwork, o.CardProcessor,
                    o.CardTransactionMode, o.ChargeCustomer, o.HourTimestamp}).IsUnique();

            foreach (var property in modelBuilder.Model.GetEntityTypes()
                .SelectMany(t => t.GetProperties())
                .Where(p => p.ClrType == typeof(decimal)))
            {
              //  property.Relational().ColumnType = "decimal(20, 4)";
                
            }

            modelBuilder.Entity<LastPaymentQuery>().HasKey(x => x.Id);
        }

        public DbSet<PaymentAggregate> PaymentAggregates { get; set; }
        public DbSet<LastQueryPoint> LastQueryPoints { get; set; }
        public DbSet<LastPaymentQuery> LastPaymentQuery { get; set; }
    }
}

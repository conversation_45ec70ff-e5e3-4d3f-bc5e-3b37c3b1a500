using Akka.Actor;
using Hubtel.Accounting.Processor.ActorSys.Messages;
using JustEat.StatsD;
using Microsoft.Extensions.Logging;
using System;
using Hubtel.Accounting.Processor.Config;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Services.Interfaces;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.ActorSys.Actors
{
    public class PosterActor : ReceiveActor
    {
        private readonly ISalesDataService _salesDataService;
        private readonly IStatsDPublisher _statsPublisher;
        private readonly ILogger _logger;
        private readonly KafkaSettings _kafkaSettings;
        private readonly AccountingHostedServiceSetting _config;
        private readonly IPosterKafkaProducer _producer;
        private readonly IBusinessInfoService _businessInfoService;

        private ICancelable Cancellation;
        public PosterActor(IStatsDPublisher statsPublisher
            , ISalesDataService salesDataService, ILogger<PosterActor> logger, 
            IOptions<KafkaSettings> kafkaSettings, 
            IOptions<AccountingHostedServiceSetting> config 
            , IPosterKafkaProducer producer, IBusinessInfoService businessInfoService)
        {
            _salesDataService = salesDataService;
            _statsPublisher = statsPublisher;
            _logger = logger;
            _kafkaSettings = kafkaSettings.Value;
            _config = config.Value;
            _producer = producer;
            _businessInfoService = businessInfoService;
            InitActor();
        }

        private void InitActor()
        {
            Receive<StartSchedulerMessage>(x =>
            {
                
                if (_config.CycleInterval<=0)
                {
                    _config.CycleInterval = 61 - DateTime.UtcNow.Minute;
                }
                Cancellation = Context.System
                .Scheduler
                    //it should run on the next hour and every hour
                .ScheduleTellRepeatedlyCancelable(TimeSpan.FromMinutes(_config.CycleInterval),
                //.ScheduleTellRepeatedlyCancelable(TimeSpan.FromSeconds(2), //just for testing
                 TimeSpan.FromHours(1),
                 Self,
                 x.Message,
                 ActorRefs.Nobody);
                _logger.LogDebug("Scheduled repeated posting activity to start at in {CycleInterval} minutes", _config.CycleInterval);
            });

            ReceiveAsync<string>(async x =>
            {
                _logger.LogDebug("About to gather payments for settlements {Date}", DateTime.UtcNow);

                var payments = await _salesDataService.GetPaymentsV2Async(HelperClass.MakeHour(DateTime.UtcNow));
                
                if (payments.Item2)
                {
                    _statsPublisher.Increment(payments.Item1.Count, "Total_To_Settle");
                    foreach (var payment in payments.Item1)
                    {
                        _logger.LogDebug("about to process transaction details {Details} for business {BusinessId}", JsonConvert.SerializeObject(payment), payment.BusinessId);
                        
                        //check if on bulk settl
                        var isBusinessOnBulkSettlement =
                            await _businessInfoService.IsBusinessOnBulkSettlement(payment.BusinessId);
                        if (!isBusinessOnBulkSettlement)
                        {
                            _logger.LogDebug("Deposit for -> {BusinessId} <-> {Key} <-> {AmountPaid} is a direct settlement. Will skip settlement *&*&*&*&",
                                payment.BusinessId, payment.Key, payment.AmountPaid);

                            continue;
                        }
                        
                        //produce to settlement
                        await _producer.ProduceAsync(_kafkaSettings.SettlementTopic, payment);
                    }
                }

                _logger.LogInformation($"Finished Batch @ {DateTime.UtcNow:yyyy'-'MM'-'dd HH':'mm':'ss'.'ffffff}");

            });

            Receive<StopSchedulerMessage>(x =>
            {
                Cancellation?.Cancel();

                _logger.LogInformation("Canceling Schedule {X}", x);

                Self.Tell(PoisonPill.Instance);
            });
        }
    }
}

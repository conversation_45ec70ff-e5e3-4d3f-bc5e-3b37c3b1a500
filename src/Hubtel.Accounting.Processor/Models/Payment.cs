using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;

namespace Hubtel.Accounting.Processor.Models
{
    public class Payment
    {
        public Payment()
        {
            Card = new CardData();
            MobileMoney = new MobileMoneyData();
        }

        public string Key { get; set; }
        public string PaymentId { get; set; }

        public string PaymentType { get; set; }
        //public string MessageId { get; set; }
        public string OrderId { get; set; }
        //public string HubtelAccountId { get; set; }
        public long FineractSavingsAccountId { get; set; }
        public string TransactionId { get; set; }
        public string ExternalTransactionId { get; set; }
        public string BusinessId { get; set; }
        
        public string BusinessName { get; set; }
        public string CustomerMobileNumber { get; set; }
        //public string CustomerName { get; set; }
        //public decimal AmountAfterCharges { get; set; }
        public decimal Fee { get; set; }
        public decimal Tips { get; set; }
        public decimal AmountPlusTips { get; set; }
        public decimal TipSettlement { get; set; }
        public bool HasTip {get;set;}
        //public decimal DeliveryFee { get; set; }
        public bool ChargeCustomer { get; set; }
        public decimal AmountPaid { get; set; }
        public decimal AmountAfterCharges { get; set; }
        public DateTime PaymentDate { get; set; }
        //public string Description { get; set; }
        //public bool IsSuccessful { get; set; }
        //public string Currency { get; set; }
        //public string Country { get; set; }

        
        public bool? IsSyncedToFineract { get; set; }
        public string SyncToFineractBatchId { get; set; }

        public CardData Card { get; set; }
        public MobileMoneyData MobileMoney { get; set; }
        //public CashData Cash { get; set; }
        //public string CallbackUrl { get; set; }
        //public string ClientReference { get; set; }
        //public string ProviderDescription { get; set; }
        //public string StatusCode { get; set; }
        public DateTime CreatedAt { get; set; }

        public decimal DeliveryFee { get; set; }
        public decimal ElevyAmount { get; set; }

        public string PaymentProcessor { get; set; }
        
        public List<Transaction> Transactions { get; set; }
    }

    public class CardData
    {
        public string CardNumber { get; set; }
        /// <summary>
        /// Indicates whether it is card present or card NOT present
        /// e.g. card_present OR card_not_present
        /// </summary>
        public string TransactionMode { get; set; }
        //public string Scheme { get; set; }
        //public string Tid { get; set; }
        //public string Authorization { get; set; }
        //public string Mid { get; set; }
        //public string CardTransactionId { get; set; }
        public string Processor { get; set; }
        
    }
    
    public class Transaction
    {
        public string MomoChannel { get; set; }
        public string OrderId { get; set; }
        public int FinerActSavingsAccountID { get; set; }
        public string ExternalTransactionID { get; set; }
        public string PaymentProcessor { get; set; }
        public decimal AmountPaid { get; set; }
        public decimal AmountAfterCharges { get; set; }
        public decimal Fee { get; set; }
        public string TransactionID { get; set; }
        public string BusinessID { get; set; }
        public string BranchId { get; set; }
        public string ClientReference { get; set; }
        public string CardTransactionMode { get; set; }
    }

    public class MobileMoneyData
    {
        public string PhoneNumber { get; set; }
        public string Network { get; set; }
        //public string Token { get; set; }
    }

    public class CashData
    {
        //public decimal AmountTendered { get; set; }
        //public decimal Balance { get; set; }
    }
    
    /// <summary>
    /// dto for groupings
    /// </summary>
    public class PaymentItems 
    {
        public long Business    { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalFees   { get; set; }
        public List<Payment> Channels { get; set; }
    }
}

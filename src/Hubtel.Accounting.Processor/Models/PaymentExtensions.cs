using System;

namespace Hubtel.Accounting.Processor.Models
{
    public static class PaymentExtensions
    {
        public static string GetPayee(this Payment message)
        {
            if ("mobilemoney".Equals(message.PaymentType, StringComparison.OrdinalIgnoreCase) || "momo".Equals(message.PaymentType, StringComparison.OrdinalIgnoreCase))
            {
                return message.MobileMoney.PhoneNumber;
            }
            if ("card".Equals(message.PaymentType, StringComparison.OrdinalIgnoreCase))
            {
                return message.Card.CardNumber;
            }
            if ("cash".Equals(message.PaymentType, StringComparison.OrdinalIgnoreCase))
            {
                return message.CustomerMobileNumber;
            }

            return string.Empty;
        }

        public static string GetFineractPaymentTypeId(this Payment message)
        {
            if ("mobilemoney".Equals(message.PaymentType, StringComparison.OrdinalIgnoreCase) || "momo".Equals(message.PaymentType, StringComparison.OrdinalIgnoreCase))
            {
                return message.MobileMoney.PhoneNumber;
            }
         
            if ("card".Equals(message.PaymentType, StringComparison.OrdinalIgnoreCase))
            {
                return message.Card.CardNumber;
            }
            if ("cash".Equals(message.PaymentType, StringComparison.OrdinalIgnoreCase))
            {
                return message.CustomerMobileNumber;
            }

            return string.Empty;
        }
    }
}
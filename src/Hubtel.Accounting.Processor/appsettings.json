{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "GELF": {"Host": "localhost", "Port": 12202, "LogSource": "Hubtel.Accounting.Processor", "Facility": "Hubtel.Accounting.Processor", "Environment": "Production", "LogLevel": {"Default": "Debug"}}}, "DatabaseSettings": {"ConnectionString": "Server=localhost;Port=5432;Database=HubtelAccountingDb;UserId=********;Password=********", "SalesReaderConnection": "Server=localhost;Port=5432;Database=HubtelUnifiedSales;UserId=********;Password=********"}, "KafkaProducerConfig": {"BootstrapServers": "localhost:9092", "Topic": ""}, "AccountingHostedServiceSetting": {"TimeInterval": 3600, "CycleInterval": 1}, "KafkaSettings": {"BootstrapServers": "localhost:9092", "ErrorTopic": "hubtel.accounting_poster.error", "ConsumerGroupId": "Hubtel.ReceiveMoneyAccounting.Poster", "SettlementTopic": "hubtel.settlement", "EnableAutoCommit": true, "PollTimeout": 10}, "AllowedHosts": "*", "StatsD": {"Host": "*************", "Port": 8125}, "ApplicationInsights": {"InstrumentationKey": "********-6699-4093-b038-c31d2875695d"}, "AccountingSdkConfig": {"BaseUrl": "http://localhost:6578", "ReceiveMoneyUrl": "http://localhost:6578", "AuthToken": ""}, "BusinessInfoConfig": {"TransactionBaseUrl": "https://transaction-bizinfo.hubtel.com/api/businesses/receive/cachedbusinessdata", "Auth": "43OUNozwz82AjZwG6BXNcBkTw4unWWES", "ElevyPosAccount": "2016598"}, "RedisConfiguration": {"Setup": [{"Host": "127.0.0.1", "Name": "payments-received-redis", "Port": "6379", "Databases": [{"alias": "payments-received-db", "db": 0}]}]}, "RedisExtra": {"CacheTransactionTimeout": 600, "Key": "Hubtel:BizInfo:"}}
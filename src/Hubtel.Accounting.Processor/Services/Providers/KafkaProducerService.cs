using System;
using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Services.Interfaces;
using Hubtel.Kafka.Host.Core;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.Services.Providers
{

    public class PosterKafkaProducer : IPosterKafkaProducer
    {
        private readonly IKafkaRawProducer _kafkaProducer;
        private readonly ILogger<KafkaProducerService> _logger;

        public PosterKafkaProducer(IKafkaRawProducer kafkaRawProducer,
            ILogger<KafkaProducerService> logger,
            IOptions<KafkaProducerConfig> kafkaProducerConfig)
        {
            _kafkaProducer = kafkaRawProducer;
            _logger = logger;
        }

        /// <summary>
        /// Produce any serializable object to the specified Kafka topic as JSON.
        /// </summary>
        public async Task ProduceAsync(string topic, object payload)
        {
            try
            {
                var json = JsonConvert.SerializeObject(payload);

              await _kafkaProducer.Produce(topic, json, dlr =>
              {
                  _logger.LogInformation("Produced payload to" +
                                         "\nTopic: {KafkaTopic}" +
                                         "\nPartition: {KafkaPartition}" +
                                         "\nStatus: {KafkaStatus}" +
                                         "\nMessage: {KafkaMessage}",
                      dlr.Topic, dlr.Partition, dlr.Status, dlr.Message.Value);
              });
              
                   
               
              
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while producing to {Topic} error -> {Message}", topic, ex.Message);
                throw new InvalidOperationException($"Failed to produce message to Kafka topic '{topic}'", ex);
            }
        }
    }

}
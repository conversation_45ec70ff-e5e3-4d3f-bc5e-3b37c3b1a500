using System;
using System.Threading.Tasks;
using Flurl.Http;
using Hubtel.Accounting.Processor.Config;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.Interfaces;
using Hubtel.Redis.Sdk.Extensions;
using Hubtel.Redis.Sdk.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using IDatabase = StackExchange.Redis.IDatabase;

namespace Hubtel.Accounting.Processor.Services.Providers { 

public class BusinessInfoService : IBusinessInfoService
{
    private readonly ILogger<BusinessInfoService> _logger;
    private readonly RedisExtra _redisExtra;
    private readonly IDatabase _redisDb;
    private readonly BusinessInfoConfig _businessInfoConfig;

    public BusinessInfoService(ILogger<BusinessInfoService> logger, IOptions<RedisExtra> redisExtra,
        IMultiRedisHostCacheRepository redisCacheRepo, IOptions<BusinessInfoConfig> businessInfoConfig)
    {
        _redisDb = redisCacheRepo.GetDb("payments-received-redis", "payments-received-db");
        _logger = logger;
        _businessInfoConfig = businessInfoConfig.Value;
        _redisExtra = redisExtra.Value;
    }

    public async Task<bool> IsBusinessOnBulkSettlement(string businessId)
    {
        try
        {
            var key = $"{_redisExtra.Key}{businessId}";

            var bizInfoCache = await _redisDb.StringGetAsync(key);

            IFlurlResponse resp = null;
            if (string.IsNullOrEmpty(bizInfoCache))
            {
                resp = await $"{_businessInfoConfig.TransactionBaseUrl}/{businessId}/no-keys"
                    .AllowAnyHttpStatus()
                    .WithHeader("Authorization", $"PrivateKey {_businessInfoConfig.Auth}")
                    .GetAsync();

                var rawResponse = await resp.ResponseMessage.Content.ReadAsStringAsync();

                _logger.LogDebug("business info request for business {Id} returned response => {Response}",
                    businessId, rawResponse);

                var response = JsonConvert.DeserializeObject<BaseResponse<BusinessInfoResponse>>(rawResponse);

                if (resp.ResponseMessage.IsSuccessStatusCode)
                {
                    await _redisDb.StringSetAsync($"{_redisExtra.Key}{businessId}",
                        JsonConvert.SerializeObject(response.Data),
                        TimeSpan.FromSeconds(_redisExtra.CacheTransactionTimeout));

                    return response.Data.IsBulkSettlement;
                }

                _logger.LogDebug(
                    "business info request for business {Id} returned response => {Response}. Will skip settlement",
                    businessId, rawResponse);

                return false; // return false to skip settlement. Internal affairs will look into it later
            }

            var bizInfoData = JsonConvert.DeserializeObject<BusinessInfoResponse>(bizInfoCache);

            _logger.LogDebug("business info request for business {Id} returned response from local cache=> {Response}",
                businessId, JsonConvert.SerializeObject(bizInfoData));

            return bizInfoData.IsBulkSettlement;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "exception {E} occured in settlement service for business {Id}. Will skip settlement",
                e.StackTrace, businessId);
            return false;
        }
    }

}

}
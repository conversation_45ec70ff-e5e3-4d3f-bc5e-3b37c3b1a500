using System;
using System.Threading.Tasks;
using Confluent.Kafka;
using Hubtel.Accounting.Processor.Config;
using Hubtel.Accounting.Processor.Models;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.Services.Providers
{

    public class ErrorProducer : IErrorProducer
    {
        private readonly IOptions<KafkaSettings> _config;
        private readonly IProducer<Null, string> _producer;

        public ErrorProducer(IOptions<KafkaSettings> config, IProducer<Null, string> producer = null)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));

            _producer = producer ?? new ProducerBuilder<Null, string>(
                new ProducerConfig
                {
                    BootstrapServers = _config.Value.BootstrapServers
                }
            ).Build();
        }

        public async Task Produce(PaymentAggregateError paymentAggregateError)
        {
            var topic = _config.Value.ErrorTopic;
            var message = JsonConvert.SerializeObject(paymentAggregateError);

            await _producer.ProduceAsync(topic, new Message<Null, string> { Value = message });
            _producer.Flush(TimeSpan.FromMilliseconds(10));
        }
    }

    public interface IErrorProducer
    {
        Task Produce(PaymentAggregateError paymentAggregateError);
    }
}
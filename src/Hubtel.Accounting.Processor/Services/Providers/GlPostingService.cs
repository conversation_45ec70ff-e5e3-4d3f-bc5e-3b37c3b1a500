using System;
using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.ProviderPostings;
using Microsoft.Extensions.Logging;

namespace Hubtel.Accounting.Processor.Services.Providers
{

    public class GlPostingService : IGlPostingService
    {
        private readonly ILogger _logger;


        private readonly IMtnGhGlPostingService _mtnGhGlPostingService;
        private readonly IVodafoneGhGlPostingService _vodafoneGhGlPostingService;
        private readonly ITigoGhGlPostingService _tigoGhGlPostingService;
        private readonly IAirtelGhGlPostingService _airtelGhGlPostingService;
        private readonly IPayworksZenithGhGlPostingService _payworksZenithGhGlPostingService;
        private readonly IEcobankGhGlPostingService _ecobankGhGlPostingService;
        private readonly IBarclaysGhGlPostingService _barclaysGhGlPostingService;
        private readonly IHubtelGlPostingService _hubtelGlPostingService;
        private readonly IGhqrGlPostingService _ghqrGlPostingService;

        public GlPostingService(ILogger<GlPostingService> logger,
            GlPostingServiceDependencies dependencies,
            IPayworksZenithGhGlPostingService payworksZenithGhGlPostingService,
            IEcobankGhGlPostingService ecobankGhGlPostingService,
            IBarclaysGhGlPostingService barclaysGhGlPostingService,
            IHubtelGlPostingService hubtelGlPostingService
            , IGhqrGlPostingService ghqrGlPostingService)
        {

            _logger = logger;
            _mtnGhGlPostingService = dependencies.MtnGhGlPostingService;
            _vodafoneGhGlPostingService = dependencies.VodafoneGhGlPostingService;
            _tigoGhGlPostingService = dependencies.TigoGhGlPostingService;
            _airtelGhGlPostingService = dependencies.AirtelGhGlPostingService;
            _payworksZenithGhGlPostingService = payworksZenithGhGlPostingService;
            _ecobankGhGlPostingService = ecobankGhGlPostingService;
            _barclaysGhGlPostingService = barclaysGhGlPostingService;
            _hubtelGlPostingService = hubtelGlPostingService;
            _ghqrGlPostingService = ghqrGlPostingService;
        }

        public async Task PostGlEntries(Payment prepareGlPostings)
        {
            try
            {
                _logger.LogDebug("doing GL posting for {PaymentType}", prepareGlPostings.PaymentType);

                await HandleMobileMoneyPayments(prepareGlPostings);
                await HandleCardPayments(prepareGlPostings);
                await HandleHubtelPayments(prepareGlPostings);
                await HandleGhqrPayments(prepareGlPostings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while posting GL entries for payment type {PaymentType}",
                    prepareGlPostings.PaymentType);
            }
        }

        #region GlEntries

        private async Task HandleMobileMoneyPayments(Payment payment)
        {
            if (!PaymentChannels.MobileMoney.Equals(payment.PaymentType, StringComparison.OrdinalIgnoreCase))
                return;

            if (PaymentChannels.MobileMoneyNetworks.MtnGh.Equals(payment.MobileMoney.Network,
                    StringComparison.OrdinalIgnoreCase))
            {
                await _mtnGhGlPostingService.PostEntry(payment);
            }
            else if (PaymentChannels.MobileMoneyNetworks.VodafoneGh.Equals(payment.MobileMoney.Network,
                         StringComparison.OrdinalIgnoreCase)
                     || PaymentChannels.MobileMoneyNetworks.VodafoneGhUssd.Equals(payment.MobileMoney.Network,
                         StringComparison.OrdinalIgnoreCase))
            {
                await _vodafoneGhGlPostingService.PostEntry(payment);
            }
            else if (PaymentChannels.MobileMoneyNetworks.TigoGh.Equals(payment.MobileMoney.Network,
                         StringComparison.OrdinalIgnoreCase))
            {
                await _tigoGhGlPostingService.PostEntry(payment);
            }
            else if (PaymentChannels.MobileMoneyNetworks.AirtelGh.Equals(payment.MobileMoney.Network,
                         StringComparison.OrdinalIgnoreCase))
            {
                await _airtelGhGlPostingService.PostEntry(payment);
            }
        }

        private async Task HandleCardPayments(Payment payment)
        {
            if (!PaymentChannels.Card.Equals(payment.PaymentType, StringComparison.CurrentCultureIgnoreCase))
                return;

            if (PaymentChannels.CardConstants.CardModes.CardPresent.Equals(payment.Card.TransactionMode,
                    StringComparison.OrdinalIgnoreCase))
            {
                await _payworksZenithGhGlPostingService.PostEntry(payment);
            }
            else if (PaymentChannels.CardConstants.CardModes.CardNotPresent.Equals(payment.Card.TransactionMode,
                         StringComparison.OrdinalIgnoreCase))
            {
                if (PaymentChannels.CardConstants.CardProcessors.Ecobank.Equals(payment.Card.Processor,
                        StringComparison.OrdinalIgnoreCase))
                {
                    await _ecobankGhGlPostingService.PostEntry(payment);
                }
                else if (PaymentChannels.CardConstants.CardProcessors.Barclays.Equals(payment.Card.Processor,
                             StringComparison.OrdinalIgnoreCase))
                {
                    await _barclaysGhGlPostingService.PostEntry(payment);
                }
            }
        }

        private async Task HandleHubtelPayments(Payment payment)
        {
            if (PaymentChannels.Hubtel.Equals(payment.PaymentType, StringComparison.OrdinalIgnoreCase)
                || PaymentChannels.HubtelGh.Equals(payment.PaymentType, StringComparison.OrdinalIgnoreCase)
                || PaymentChannels.HubtelKe.Equals(payment.PaymentType, StringComparison.OrdinalIgnoreCase))
            {
                if (PaymentChannels.HubtelGh.Equals(payment.PaymentType, StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogInformation("posting for hubtel-gh");
                }
                else if (PaymentChannels.HubtelKe.Equals(payment.PaymentType, StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogInformation("posting for hubtel-ke");
                }

                await _hubtelGlPostingService.PostEntry(payment);
            }
        }

        private async Task HandleGhqrPayments(Payment payment)
        {
            if (PaymentChannels.Ghqr.Equals(payment.PaymentType, StringComparison.OrdinalIgnoreCase))
            {
                await _ghqrGlPostingService.PostEntry(payment);
            }
        }

        #endregion
    }

    public interface IGlPostingService
    {
        Task PostGlEntries(Payment prepareGlPostings);
    }

}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hubtel.Accounting.Data;
using Hubtel.Accounting.Data.EntityModels;
using Hubtel.Accounting.Data.Repository;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.Interfaces;
using Microsoft.ApplicationInsights;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Npgsql;

namespace Hubtel.Accounting.Processor.Services.Providers
{

    public class SalesDataService : ISalesDataService
    {
        private readonly ILogger _logger;
        private readonly AccountingDbContext _accountingDbContext;
        private readonly UnifiedSalesContext _unifiedSalesContext;
        private readonly TelemetryClient _telemetryClient;
        private readonly IPaymentQueryRepository _paymentQueryRepository;

        public SalesDataService(TelemetryClient telemetryClient,
            ILogger<SalesDataService> logger, AccountingDbContext accountingDbContext,
            UnifiedSalesContext unifiedSalesContext, IPaymentQueryRepository paymentQueryRepository)
        {
            _logger = logger;
            _accountingDbContext = accountingDbContext;
            _unifiedSalesContext = unifiedSalesContext;
            _telemetryClient = telemetryClient;
            _paymentQueryRepository = paymentQueryRepository;
        }

        public async Task<(List<Payment>, bool, DateTime, DateTime)> GetPaymentsAsync(DateTime theendDate)
        {
            try
            {
                var pays = new List<Payment>();

                var now = DateTime.UtcNow;
                theendDate = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 0);
                var thestartDate = theendDate.AddHours(-1);

                var payments = await _unifiedSalesContext.Payments
                    .Where(x => x.CreatedAt >= thestartDate
                                && x.CreatedAt < theendDate
                                && x.IsSuccessful
                                && x.PaymentType.ToLower() != "cash"
                                && x.PaymentType.ToLower() != "bankpay")
                    .OrderBy(x => x.CreatedAt)
                    .AsNoTracking().ToListAsync();

                if (payments.Count == 0)
                {
                    _logger.LogWarning($"no payments found in db. Will wait for next cycle");
                    return (new List<Payment>(), false, DateTime.UtcNow, DateTime.UtcNow);
                }

                _logger.LogInformation(
                    "fetched {PaymentCount} from Payments table. Will group by FineractSavingsAccountId. Date between {StartDate} and {EndDate}",
                    payments.Count, thestartDate, theendDate);
                var groupedPayments = payments.GroupBy(x => x.FineractSavingsAccountId).Select(x => new
                {
                    Business = x.Key,

                    TotalAmount = x.Sum(r => r.AmountPaid), // - r.DeliveryFee),
                    TotalFees = x.Sum(r => r.Charges),
                    Channels = x
                        .GroupBy(v => v.PaymentProcessor ?? v.MomoChannel ?? v.CardTransactionMode ?? v.PaymentType)
                        .Select(n => new Payment
                        {

                            PaymentId = string.Join(",", n.Select(mn => $"'{mn.Id}'")),

                            Key = n.Key,
                            TransactionId = $"{GetPGDatetimeFormat(n.Min(m => m.CreatedAt))}-{x.Key}",
                            ExternalTransactionId = $"{GetPGDatetimeFormat(x.Max(m => m.CreatedAt))}",
                            OrderId = $"{GetPGDatetimeFormat(payments[0].CreatedAt)}",
                            PaymentType = n.First().PaymentType,
                            Fee = n.Sum(b => b.Charges),
                            Tips = n.Sum(b => b.Tips),
                            AmountPlusTips = n.Sum(b => b.AmountPlusTips),
                            TipSettlement = n.Sum(b => b.TipSettlement),
                            AmountPaid = n.Sum(b =>
                                ((b.Charges + b.AmountAfterCharges) >= b.AmountPaid
                                    ? b.Charges + b.AmountAfterCharges
                                    : b.AmountPaid) - b.DeliveryFee - b.ElevyAmount -
                                b.TipSettlement), //Subtracting delivery fee, e-levy and TipSettlement from amount paid. take notice
                            AmountAfterCharges = n.Sum(b => b.AmountAfterCharges),
                            ElevyAmount = n.Sum(b => b.ElevyAmount),
                            FineractSavingsAccountId = x.Key,
                            BusinessId = n.First().BusinessId,
                            CreatedAt = n.Max(m => m.CreatedAt),
                            PaymentDate = n.Max(m => m.PaymentDate),
                            MobileMoney = new MobileMoneyData
                            {
                                Network = n.First().PaymentType
                                    .Equals("mobilemoney", StringComparison.OrdinalIgnoreCase) || n.First().PaymentType
                                    .Equals("momo", StringComparison.OrdinalIgnoreCase)
                                    ? n.First().MomoChannel
                                    : null
                            },
                            Card = new CardData
                            {
                                Processor = n.First().CardProcessor,
                                TransactionMode =
                                    n.First().PaymentType.Equals("card", StringComparison.CurrentCultureIgnoreCase)
                                        ? n.Key
                                        : null,
                            },
                            PaymentProcessor = n.Key
                        })
                });



                foreach (var item in groupedPayments)
                {
                    pays.AddRange(item.Channels);

                    //telemetry
                    _telemetryClient.GetMetric(item.Business.ToString()).TrackValue((double)item.TotalAmount);

                    _telemetryClient.TrackEvent($"Settling {item.Business}",
                        item.Channels.GroupBy(v => v.Key)
                            .ToDictionary(x => x.Key, x => x.Sum(o => o.AmountPaid).ToString()));
                }

                _logger.LogDebug("found {Count} groups from current resultset", pays.Count);

                var res = await StoreLastQueryPointAsync(new LastPaymentQuery
                {
                    Id = theendDate,
                    CreatedAt = DateTime.UtcNow
                });

                return (pays, res, thestartDate, theendDate);
            }
            catch (Exception ex)
            {
                _telemetryClient.TrackException(ex);
                _logger.LogError(ex, "Couldn't fetch payments from database {Exception}", ex.Message);
                return (new List<Payment>(), false, DateTime.UtcNow, DateTime.UtcNow);
            }

        }

        public async Task<(List<Payment>, bool, DateTime, DateTime)> GetPaymentsV2Async(DateTime theendDate)
        {
            try
            {
                var pays = new List<Payment>();

                var now = DateTime.UtcNow;
                theendDate = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 0);
                var thestartDate = theendDate.AddHours(-1);

                var payments = await _paymentQueryRepository.GetPaymentsAsync(thestartDate, theendDate);

                if (payments.Count == 0)
                {
                    _logger.LogWarning($"no payments found in db. Will wait for next cycle");
                    return (new List<Payment>(), false, DateTime.UtcNow, DateTime.UtcNow);
                }

                _logger.LogInformation(
                    "Fetched {PaymentCount} from Payments table. Will group by FineractSavingsAccountId. Date between {StartDate} and {EndDate}",
                    payments.Count, thestartDate, theendDate);

                var groupedPayments = GroupPayments(payments);

                foreach (var item in groupedPayments)
                {
                    pays.AddRange(item.Channels);

                    //telemetry
                    _telemetryClient.GetMetric(item.Business.ToString()).TrackValue((double)item.TotalAmount);

                    _telemetryClient.TrackEvent($"Settling {item.Business}",
                        item.Channels.GroupBy(v => v.Key)
                            .ToDictionary(x => x.Key, x => x.Sum(o => o.AmountPaid).ToString()));
                }

                _logger.LogDebug("found {Count} groups from current result set", pays.Count);

                var res = await StoreLastQueryPointAsync(new LastPaymentQuery
                {
                    Id = theendDate,
                    CreatedAt = DateTime.UtcNow
                });

                return (pays, res, thestartDate, theendDate);
            }
            catch (Exception ex)
            {
                _telemetryClient.TrackException(ex);
                _logger.LogError(ex, "Couldn't fetch payments from database {Exception}", ex.Message);
                return (new List<Payment>(), false, DateTime.UtcNow, DateTime.UtcNow);
            }
        }

        #region Groupings

        public IEnumerable<PaymentItems> GroupPayments(List<NewPayment> payments)
        {
            return payments.GroupBy(x => x.FineractSavingsAccountId)
                .Select(accountGroup => new PaymentItems
                {
                    Business = accountGroup.Key,
                    TotalAmount = accountGroup.Sum(r => r.AmountPaid),
                    TotalFees = accountGroup.Sum(r => r.Charges),
                    Channels = accountGroup
                        .GroupBy(v => v.PaymentProcessor ?? v.MomoChannel ?? v.CardTransactionMode ?? v.PaymentType)
                        .Select(channelGroup => CreatePayment(channelGroup, accountGroup.Key, payments[0].CreatedAt,
                            accountGroup.Max(m => m.CreatedAt), accountGroup.Max(m => m.PaymentDate)))
                        .ToList()
                });
        }

        private Payment CreatePayment(
            IGrouping<string, NewPayment> group,
            long accountId,
            DateTime firstCreated,
            DateTime maxCreated,
            DateTime maxPaymentDate)
        {
            var first = group.First();

            return new Payment
            {
                PaymentId = string.Join(",", group.Select(mn => $"'{mn.Id}'")),
                Key = group.Key,
                TransactionId = $"{GetPGDatetimeFormat(group.Min(m => m.CreatedAt))}-{accountId}",
                ExternalTransactionId = $"{GetPGDatetimeFormat(maxCreated)}",
                OrderId = $"{GetPGDatetimeFormat(firstCreated)}",
                PaymentType = first.PaymentType,
                Fee = group.Sum(b => b.Charges),
                Tips = group.Sum(b => b.Tips),
                AmountPlusTips = group.Sum(b => b.AmountPlusTips),
                TipSettlement = group.Sum(b => b.TipSettlement),
                AmountPaid = group.Sum(b =>
                    ((b.Charges + b.AmountAfterCharges) >= b.AmountPaid
                        ? b.Charges + b.AmountAfterCharges
                        : b.AmountPaid) - b.DeliveryFee - b.ElevyAmount - b.TipSettlement),
                AmountAfterCharges = group.Sum(b => b.AmountAfterCharges),
                ElevyAmount = group.Sum(b => b.ElevyAmount),
                FineractSavingsAccountId = accountId,
                BusinessId = first.BusinessId,
                BusinessName = first.BusinessName,
                CreatedAt = maxCreated,
                PaymentDate = maxPaymentDate,
                Transactions = group.Select(CreateTransaction).ToList(),
                MobileMoney = new MobileMoneyData
                {
                    Network = first.PaymentType.Equals("mobilemoney", StringComparison.CurrentCultureIgnoreCase) ||
                              first.PaymentType.Equals("momo", StringComparison.CurrentCultureIgnoreCase)
                        ? first.MomoChannel
                        : null
                },
                Card = new CardData
                {
                    Processor = first.CardProcessor,
                    TransactionMode = first.PaymentType.Equals("card", StringComparison.CurrentCultureIgnoreCase)
                        ? group.Key
                        : null
                },
                PaymentProcessor = group.Key
            };
        }

        private Transaction CreateTransaction(NewPayment payment)
        {
            return new Transaction
            {
                MomoChannel = payment.MomoChannel,
                OrderId = payment.OrderId,
                FinerActSavingsAccountID = payment.FineractSavingsAccountId,
                ExternalTransactionID = payment.ExternalTransactionId,
                PaymentProcessor = payment.PaymentProcessor,
                AmountPaid =
                    ((payment.Charges + payment.AmountAfterCharges) >= payment.AmountPaid
                        ? payment.Charges + payment.AmountAfterCharges
                        : payment.AmountPaid) - payment.DeliveryFee - payment.ElevyAmount - payment.TipSettlement,
                TransactionID = payment.TransactionId,
                Fee = payment.Charges,
                AmountAfterCharges = payment.AmountAfterCharges,
                BusinessID = payment.BusinessId,
                BranchId = payment.BranchId,
                ClientReference = payment.ClientReference,
                CardTransactionMode = payment.CardTransactionMode
            };
        }

        #endregion


        private static string GetPGDatetimeFormat(DateTime date)
        {
            return date.ToString("yyyy'-'MM'-'dd HH':'mm':'ss'.'ffffff");
        }

        public async Task<LastPaymentQuery> GetLastQueryPointAsync()
        {
            try
            {
                var lastQuery = await _accountingDbContext.LastPaymentQuery.AsNoTracking().OrderByDescending(x => x.Id)
                    .FirstOrDefaultAsync();
                if (lastQuery == null)
                {
                    lastQuery = new LastPaymentQuery
                    {
                        Id = DateTime.UtcNow,
                        CreatedAt = DateTime.UtcNow
                    };
                }

                return lastQuery;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Couldn't update last query point from database {Message}:", ex.Message);
                return null;
            }
        }

        public async Task<bool> StoreLastQueryPointAsync(LastPaymentQuery lastQueryPoint)
        {
            try
            {
                _logger.LogDebug("storing last query point {CreatedAt} with ID= {Id}", lastQueryPoint.CreatedAt,
                    lastQueryPoint.Id);
                _accountingDbContext.LastPaymentQuery.Add(lastQueryPoint);
                await _accountingDbContext.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _telemetryClient.TrackEvent("Failed Saving Last Query Point", new Dictionary<string, string>
                {
                    { "Date", $"Id:{lastQueryPoint.Id} Date:{lastQueryPoint.CreatedAt}" }
                });

                _logger.LogError(ex,
                    "Couldn't store or update last store point. Will terminate this process. Exception=> {Message}",
                    ex.Message);
                //Stop application if an error occuring in relation to last query point
                Environment.Exit(0);
                return false;
            }
        }

        public async Task<int> UpdatePaymentSSyncedStatusAsync(string ids, string batchId, DateTime start, DateTime end,
            bool status)
        {
            try
            {
                int count = 0;
                NpgsqlConnection connection = (NpgsqlConnection)_unifiedSalesContext.Database.GetDbConnection();

                var datetime = DateTime.UtcNow;
                var date = datetime.AddHours(-1).Date;
                var monthCheck = (date.Month >= 1 && date.Month <= 9) ? $"0{date.Month}" : date.Month.ToString();
                var dayCheck = (date.Day >= 1 && date.Day <= 9) ? $"0{date.Day}" : date.Day.ToString();

                var tableName = $"Payments_{date.Year}_{monthCheck}_{dayCheck}";

                string sql =
                    $"Update \"{tableName}\" SET \"IsSyncedToFineract\"= @p1 , \"SyncToFineractBatchId\"= @p2  Where \"CreatedAt\" >= @p3 and \"CreatedAt\" <= @p4 and  \"Id\" in ({ids})";
                _logger.LogDebug("About to run ->>> {Sql} <<<-", sql);
                NpgsqlCommand command = new NpgsqlCommand(sql, connection);

                command.Parameters.Add(new NpgsqlParameter("p1", status));
                command.Parameters.Add(new NpgsqlParameter("p2", batchId));
                command.Parameters.Add(new NpgsqlParameter("p3", start));
                command.Parameters.Add(new NpgsqlParameter("p4", end));
                await connection.OpenAsync();
                count = await command.ExecuteNonQueryAsync();
                await connection.CloseAsync();



                _logger.LogDebug("Success Updating PaymentIds --- count {Count}", count);

                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating payment IDs {PaymentIds}", ids);
                return -1;
            }
        }
    }

}
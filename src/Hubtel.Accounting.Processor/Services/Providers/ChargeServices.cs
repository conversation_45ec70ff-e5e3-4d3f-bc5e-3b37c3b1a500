using System;
using System.Linq;
using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Config;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Sdk;
using Hubtel.Accounting.Sdk.Constants;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.Services.Providers { 

public class ChargeService : IChargeService
{
    private readonly ILogger _logger;
        
        
        
    private readonly IErrorProducer _errorProducer;
    private readonly IAccountingApi _accountingApi;
    private readonly AccountingSettings _config;

    public ChargeService(ILogger<ChargeService> logger
        ,IErrorProducer errorProducer
        , IAccountingApi accountingApi
        ,IOptions<AccountingSettings> config)
    {
        _logger = logger;
            
            
        _errorProducer = errorProducer;
        _accountingApi = accountingApi;
        _config = config.Value;
    }

    public async Task CreateCharge(Payment payment)
    {
        try
        {
            if (payment.Fee <= 0)
            {
                return;
            }

            var charge = _config.ChargeIds.FirstOrDefault(c =>
                c.Name.Equals(payment.PaymentType, StringComparison.CurrentCultureIgnoreCase));

            if (charge == null)
            {
                await _errorProducer.Produce(new PaymentAggregateError
                {
                    Data = payment,
                    Message = $"unrecognised payment type {payment.PaymentType}. Will ignore",
                    Stage = PaymentErrors.Create_Charge_type_error
                });
                _logger.LogWarning("unrecognised payment type {PaymentType}. Will ignore", payment.PaymentType);
                return;
            }

            var channel = payment.MobileMoney.Network ?? payment.MobileMoney.Network ?? payment.Card.TransactionMode ?? payment.PaymentType;

            var chargeId = charge.Channels.FirstOrDefault(c =>
                c.Name.Equals(channel, StringComparison.CurrentCultureIgnoreCase));

            if (chargeId == null)
            {
                await _errorProducer.Produce(new PaymentAggregateError
                {
                    Data = payment,
                    Message = $"could not determine charge ID for {payment.TransactionId}. Will ignore.",
                    Stage = PaymentErrors.Create_Charge_type_error
                });
                _logger.LogWarning("could not determine charge ID for payment payload {Payment} with channel {Channel}. Will ignore.", JsonConvert.SerializeObject(payment), channel);                return;
            }


            if (payment.Fee < 0)
            {
                _logger.LogDebug("payment fee is zero, will not execute. payload:{Payment}", JsonConvert.SerializeObject(payment));
                return;
            }
                
                
                
                
            var savingChargeRmRequest = new AddChargeRequest
            {
                Amount = payment.Fee,
                AccountingType = AccountingType.ReceiveMoney,
                ChargeId = chargeId.Id,
                DueTimeStamp = payment.PaymentDate,
                AccountNumber = payment.FineractSavingsAccountId.ToString(),
            };


            var resRm = await _accountingApi.AddCharge(savingChargeRmRequest);
                
            _logger.LogDebug("Rm charge request: {Request} ==> Rm charge response: {Response}", savingChargeRmRequest, resRm);

            if (!resRm.IsSuccessful)
            {
                await _errorProducer.Produce(new PaymentAggregateError
                {
                    Data = payment,
                    Message = $"could not post charge for transaction {payment.TransactionId} to accounting. {resRm.RawResponse}.Will ignore posting apply charges.",
                    Stage = PaymentErrors.Create_Charge_type_error
                });
                _logger.LogWarning("could not post charge for transaction {TransactionId} to accounting. {RawResponse}.Will ignore posting apply charges.", payment.TransactionId, resRm.RawResponse);
                return;
            }
              
            await PayRmCharge(payment, resRm.Data.ResourceId);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while creating charge");
        }
            
    }
        
    public async Task PayRmCharge(Payment payment, long resourceId)
    {
        var payRmSavingsChargeRequest = new PayChargeRequest
        {
            Amount = payment.Fee,
            DueTimeStamp = payment.PaymentDate,
            PayChargeId = resourceId,
            AccountingType = AccountingType.ReceiveMoney,
            Description = $"FEE: Transaction Processing Fee - {payment.FineractSavingsAccountId}"
        };
        
        _logger.LogDebug("pay charge request to receive money fineract for {AccountId}: {Request}", payment.FineractSavingsAccountId, JsonConvert.SerializeObject(payRmSavingsChargeRequest));
            
        var resRm = await _accountingApi.PayCharge(payRmSavingsChargeRequest);
        
        _logger.LogDebug("pay charge response for receive money fineract for {AccountId}: {Response}", payment.FineractSavingsAccountId, JsonConvert.SerializeObject(resRm));
        
        if (!resRm.IsSuccessful)
        {
            await _errorProducer.Produce(new PaymentAggregateError
            {
                Data = payment,
                Message = $"could not pay charge for transaction {payment.TransactionId} to accounting. {resRm.RawResponse}.",
                Stage = PaymentErrors.Pay_Charge_type_error
            });
            _logger.LogWarning("could not pay charge for transaction {TransactionId} to accounting. {RawResponse}.", payment.TransactionId, resRm.RawResponse);
        }
    }

    public async Task PayCharge(Payment payment, long resourceId)
    {
        //var paySavingsChargeRequest = new PaySavingsChargeRequest
        var paySavingsChargeRequest = new PayChargeRequest
        {
            Amount = payment.Fee,
            DueTimeStamp = payment.PaymentDate,
            PayChargeId = resourceId
        };
            
           
        
        _logger.LogDebug("pay charge request to fineract for {AccountId}: {Request}", payment.FineractSavingsAccountId, JsonConvert.SerializeObject(paySavingsChargeRequest));

        var res = await _accountingApi.PayCharge(paySavingsChargeRequest);

        if (!res.IsSuccessful)
        {
            await _errorProducer.Produce(new PaymentAggregateError
            {
                Data = payment,
                Message = $"could not pay charge for transaction {payment.TransactionId} to accounting. {res.RawResponse}.",
                Stage = PaymentErrors.Pay_Charge_type_error
            });
            _logger.LogWarning("could not pay charge for transaction {TransactionId} to accounting. {RawResponse}.", payment.TransactionId, res.RawResponse);
        }
    }
}


public interface IChargeService
{
    Task CreateCharge(Payment payment);
}
}
using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Config;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Sdk;
using Hubtel.Accounting.Sdk.Constants;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.Services.Providers
{

    public class DepositService : IDepositService
    {
        private readonly ILogger _logger;
        private readonly IOptions<AccountingSettings> _accountingSettings;
        private readonly IChargeService _chargeService;
        private readonly IGlPostingService _glPostingService;
        private readonly IErrorProducer _errorProducer;
        private readonly IAccountingApi _accountingApi;
        private readonly BusinessInfoConfig _businessInfoConfig;

        public DepositService(ILogger<DepositService> logger,
            IOptions<AccountingSettings> accountingSettings,
            IChargeService chargeService,
            IGlPostingService glPostingService,
            IAccountingApi accountingApi,
            IErrorProducer errorProducer,
            IOptions<BusinessInfoConfig> businessInfoConfig)
        {
            _logger = logger;
            _accountingSettings = accountingSettings;
            _chargeService = chargeService;
            _glPostingService = glPostingService;
            _errorProducer = errorProducer;
            _businessInfoConfig = businessInfoConfig.Value;
            _accountingApi = accountingApi;
        }

        public async Task<bool> MakeDeposit(Payment payment)
        {
            try
            {
                if ((payment.AmountPaid - payment.Fee) < 0)
                {
                    return false;
                }

                if (payment.FineractSavingsAccountId == 0)
                {
                    return false;
                }

                var paymentType = _accountingSettings.Value.PaymentTypes.FirstOrDefault(t =>
                    t.Name.Equals(payment.PaymentType, StringComparison.CurrentCultureIgnoreCase));


                if (paymentType == null)
                {
                    await _errorProducer.Produce(new PaymentAggregateError
                    {
                        Data = payment,
                        Message = $"unrecognized payment type {payment.PaymentType}. Will post as cash.",
                        Stage = PaymentErrors.Payment_type_error
                    });
                    _logger.LogWarning("unrecognized payment type {PaymentType}. Will post as cash.",
                        payment.PaymentType);
                    return false;
                }

                if (_accountingSettings.Value.ExcludedBusinesses.Contains(payment.BusinessId)
                    && payment.PaymentType.Equals("paysmallsmall", StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogDebug("deposit  for -> {BusinessId} <-> " +
                                     "{TransactionId} <-> {PaymentType} is an ECG pay small small order." +
                                     "Will skip settlement *&*&*&*&",
                        payment.BusinessId, payment.TransactionId, payment.PaymentType);
                    return true;
                }

                var receiveMoneyDepositRequest = new Accounting.Sdk.Models.DepositRequest
                {
                    AccountNumber = payment.FineractSavingsAccountId.ToString(),
                    CheckNumber =
                        $"{payment.ExternalTransactionId}-{(payment.MobileMoney.Network ?? payment.Card.TransactionMode ?? payment.PaymentType)}",
                    AccountingType = AccountingType.ReceiveMoney,
                    PaymentTypeId = paymentType.Id.ToString(),
                    RoutingCode =
                        $"{payment.OrderId}-{(payment.MobileMoney.Network ?? payment.Card.TransactionMode ?? payment.PaymentType)}",
                    TransactionDate = payment.PaymentDate,
                    TransactionAmount = payment.AmountPaid,
                    DateFormat = "dd MMMM yyy",
                    Description =
                        $"HOURLY SETTLEMENT: Payments before {DateTime.UtcNow.Date.AddHours(DateTime.UtcNow.Hour).ToString("h:mmtt", CultureInfo.InvariantCulture).ToLower()}"
                };

                _logger.LogDebug("Deposit request to receive money fineract for {AccountId}: {Request}",
                    payment.FineractSavingsAccountId, JsonConvert.SerializeObject(receiveMoneyDepositRequest));

                var rmResponse = await _accountingApi.Deposit(receiveMoneyDepositRequest);

                _logger.LogDebug("--> Deposit Response to receive money fineract for {AccountId}: {Response}",
                    payment.FineractSavingsAccountId, JsonConvert.SerializeObject(rmResponse));

                if (!rmResponse.IsSuccessful)
                {
                    await _errorProducer.Produce(new PaymentAggregateError
                    {
                        Data = payment,
                        Message =
                            $"could not post transaction {payment.TransactionId} to accounting. {(rmResponse.RawResponse != null ? rmResponse.RawResponse : "Could not reach fineract")}.Will ignore posting charges.",
                        Stage = PaymentErrors.Deposit_error
                    });
                    _logger.LogWarning(
                        "could not post transaction {TransactionId} with details {RequestDetails} to accounting. {RawResponse}. Will ignore posting charges.",
                        payment.TransactionId, JsonConvert.SerializeObject(receiveMoneyDepositRequest),
                        rmResponse.RawResponse ?? "Could not reach fineract");
                    return rmResponse.IsSuccessful;
                }

                await _chargeService.CreateCharge(payment);

                if (payment.MobileMoney?.Network != "mtn-gh-cbg")
                {
                    await _glPostingService.PostGlEntries(payment);
                }

                //pay e-levy
                if (payment.ElevyAmount > 0)
                {
                    var elevyDepositRequest = new Accounting.Sdk.Models.DepositRequest
                    {
                        AccountNumber = _businessInfoConfig.ElevyPosAccount,
                        AccountingType = AccountingType.ReceiveMoney,
                        TransactionDate = payment.PaymentDate,
                        TransactionAmount = payment.ElevyAmount,
                        DateFormat = "dd MMMM yyy",
                        Description =
                            $"HOURLY SETTLEMENT: Payments before {DateTime.UtcNow.Date.AddHours(DateTime.UtcNow.Hour).ToString("h:mmtt", CultureInfo.InvariantCulture).ToLower()}"
                    };

                    var elevyDepositResponse = await _accountingApi.Deposit(elevyDepositRequest);

                    _logger.LogDebug("elevy deposit request => {Request} response {Response}.",
                        JsonConvert.SerializeObject(elevyDepositRequest),
                        JsonConvert.SerializeObject(elevyDepositResponse));
                }

                return rmResponse.IsSuccessful;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while making deposit for transaction {TransactionId}",
                    payment?.TransactionId);
                return false;
            }
        }
    }


    public interface IDepositService
    {
        Task<bool> MakeDeposit(Payment payment);
    }

}
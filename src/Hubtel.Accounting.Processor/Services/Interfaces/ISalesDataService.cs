using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Hubtel.Accounting.Data.EntityModels;
using Hubtel.Accounting.Processor.Models;

namespace Hubtel.Accounting.Processor.Services.Interfaces
{

    public interface ISalesDataService
    {
        Task<LastPaymentQuery> GetLastQueryPointAsync();

        Task<(List<Payment>, bool, DateTime, DateTime)> GetPaymentsAsync(DateTime theendDate);

        Task<(List<Payment>, bool, DateTime, DateTime)> GetPaymentsV2Async(DateTime theendDate);

        Task<int> UpdatePaymentSSyncedStatusAsync(string ids, string batchId, DateTime start, DateTime end,
            bool status);
    }
}
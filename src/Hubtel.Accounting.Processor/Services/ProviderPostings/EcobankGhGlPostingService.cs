using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk;
using Hubtel.Accounting.Sdk.Models;

namespace Hubtel.Accounting.Processor.Services.ProviderPostings
{
    public class EcobankGhGlPostingService : IEcobankGhGlPostingService
    {
        
        private readonly ILogger _logger;
        private readonly IErrorProducer _errorProducer;
       
        private readonly IAccountingApi _accountingApi;

        public EcobankGhGlPostingService(
            
            ILogger<EcobankGhGlPostingService> logger,
            IErrorProducer errorProducer
            
            ,IAccountingApi accountingApi)
        {
            
            _logger = logger;
            _errorProducer = errorProducer;
           
            _accountingApi = accountingApi;
        }

        public async Task PostEntry(Payment postCardNotPresentEcobankGhGl)
        {

            //todo: let's hold on here
            //we have a problem with rounding for lower AmountPaid values
            //accounting API rejects our GL postings
            //we have to consult and re-program
            var request = new GlRequest
            {
                CurrencyCode = "GHC",
                Comments = $"{Constants.PaymentChannels.CardConstants.CardModes.CardNotPresent} - {Constants.PaymentChannels.CardConstants.CardProcessors.Ecobank}",
                TimeStamp = postCardNotPresentEcobankGhGl.PaymentDate,
                OfficeId = 2

            };

            //credit general merchant ledger
            request.Credits.Add(new GlDataModel
            {
                Amount = postCardNotPresentEcobankGhGl.AmountAfterCharges,
                GlAccountId = 8
            });

            //credit provider fee ledger
            request.Credits.Add(new GlDataModel
            {
                Amount = postCardNotPresentEcobankGhGl.Fee,
                GlAccountId = 20
            });

            //debit provider's funds ledger
            request.Debits.Add(new GlDataModel
            {
                Amount = postCardNotPresentEcobankGhGl.AmountPaid,
                GlAccountId = 7
            });

            _logger.LogWarning($"gl request {JsonConvert.SerializeObject(request)}");
            var res = await _accountingApi.PostGl(request);

            if (!res.IsSuccessful)
            {
                await _errorProducer.Produce(new PaymentAggregateError
                {
                    Data = postCardNotPresentEcobankGhGl,
                    Message = $"could not POST GL entries successfully for CARD-GH. Account Number: {postCardNotPresentEcobankGhGl.FineractSavingsAccountId}. Response: {res.RawResponse}",
                    Stage = PaymentErrors.Post_Gl_type_error
                });
                _logger.LogWarning(
                    $"could not POST GL entries successfully for CARD-GH. Account Number: {postCardNotPresentEcobankGhGl.FineractSavingsAccountId}. Response: {res.RawResponse}");
                return;
            }

            _logger.LogInformation($"successfully POSTED GL entries for account number {postCardNotPresentEcobankGhGl.FineractSavingsAccountId} on {Constants.PaymentChannels.CardConstants.CardModes.CardNotPresent} - {Constants.PaymentChannels.CardConstants.CardProcessors.Ecobank}");
        }
    }

    public interface IEcobankGhGlPostingService
    {
        Task PostEntry(Payment message);
    }
}

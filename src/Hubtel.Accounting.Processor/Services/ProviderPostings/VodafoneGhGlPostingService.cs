using System;
using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk;
using Hubtel.Accounting.Sdk.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.Services.ProviderPostings
{
    public class VodafoneGhGlPostingService : IVodafoneGhGlPostingService
    {
        
        private readonly ILogger _logger;
        private readonly IErrorProducer _errorProducer;
     //   private readonly IJournalEntriesApi _journalEntriesApi;
        private readonly IAccountingApi _accountingApi;

        public VodafoneGhGlPostingService(
            
            ILogger<VodafoneGhGlPostingService> logger,
            IErrorProducer errorProducer
            //, IJournalEntriesApi journalEntriesApi
            ,IAccountingApi accountingApi)
        {
            
            _logger = logger;
            _errorProducer = errorProducer;
            //_journalEntriesApi = journalEntriesApi;
            _accountingApi = accountingApi;
        }

        public async Task PostEntry(Payment message)
        {
            var request = new GlRequest
            {
                CurrencyCode = "GHC",
                //Comments = $"{Constants.PaymentChannels.MobileMoneyNetworks.VodafoneGh}",
                Comments = string.IsNullOrEmpty(message.PaymentProcessor) ? message.MobileMoney.Network : message.PaymentProcessor,
                TimeStamp = message.PaymentDate,
                OfficeId = 2

            };

            if (string.IsNullOrEmpty(message.PaymentProcessor))
            {
                //credit general merchant ledger
                request.Credits.Add(new GlDataModel
                {
                    Amount = message.AmountAfterCharges,
                    GlAccountId = 8
                });

                //credit provider fee ledger
                request.Credits.Add(new GlDataModel
                {
                    Amount = message.Fee,
                    GlAccountId = 16
                });

                //debit provider's funds ledger
                request.Debits.Add(new GlDataModel
                {
                    Amount = message.AmountPaid,
                    GlAccountId = 4
                });
            }
            else
            {
                if ("Vodafone GH Receive Money - Default".Equals(message.PaymentProcessor, StringComparison.OrdinalIgnoreCase))
                {
                    //credit general merchant ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.AmountAfterCharges,
                        GlAccountId = 8
                    });

                    //credit provider fee ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.Fee,
                        GlAccountId = 16
                    });

                    //debit provider's funds ledger
                    request.Debits.Add(new GlDataModel
                    {
                        Amount = message.AmountPaid,
                        GlAccountId = 4
                    });

                }

                if ("Vodafone GH Receive Money - Betting".Equals(message.PaymentProcessor, StringComparison.OrdinalIgnoreCase))
                {
                    //credit general merchant ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.AmountAfterCharges,
                        GlAccountId = 8
                    });

                    //credit provider fee ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.Fee,
                        GlAccountId = 70
                    });

                    //debit provider's funds ledger
                    request.Debits.Add(new GlDataModel
                    {
                        Amount = message.AmountPaid,
                        GlAccountId = 67
                    });

                }

                if ("Vodafone GH Receive Money - Instore".Equals(message.PaymentProcessor, StringComparison.OrdinalIgnoreCase))
                {
                    //credit general merchant ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.AmountAfterCharges,
                        GlAccountId = 8
                    });

                    //credit provider fee ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.Fee,
                        GlAccountId = 71
                    });

                    //debit provider's funds ledger
                    request.Debits.Add(new GlDataModel
                    {
                        Amount = message.AmountPaid,
                        GlAccountId = 68
                    });

                }

                if ("Vodafone GH Receive Money - NDC".Equals(message.PaymentProcessor, StringComparison.OrdinalIgnoreCase))
                {
                    //credit general merchant ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.AmountAfterCharges,
                        GlAccountId = 8
                    });

                    //credit provider fee ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.Fee,
                        GlAccountId = 72
                    });

                    //debit provider's funds ledger
                    request.Debits.Add(new GlDataModel
                    {
                        Amount = message.AmountPaid,
                        GlAccountId = 69
                    });

                }

            }


            _logger.LogWarning($"gl request {JsonConvert.SerializeObject(request)}");
            //var res = await _journalEntriesApi.Post(request);
            var res = await _accountingApi.PostGl(request);

            if (!res.IsSuccessful)
            {
                await _errorProducer.Produce(new PaymentAggregateError
                {
                    Data = message,
                    Message = $"could not POST GL entries successfully for {Constants.PaymentChannels.MobileMoneyNetworks.VodafoneGh}. Account Number: {message.FineractSavingsAccountId}.Response: {res.RawResponse}",
                    Stage = PaymentErrors.Post_Gl_type_error
                });
                _logger.LogWarning(
                    $"could not POST GL entries successfully for {Constants.PaymentChannels.MobileMoneyNetworks.VodafoneGh}. Account Number: {message.FineractSavingsAccountId}.Response: {res.RawResponse}");
                return;
            }

            _logger.LogInformation($"successfully POSTED GL entries for account number {message.FineractSavingsAccountId} on {Constants.PaymentChannels.MobileMoneyNetworks.VodafoneGh}");
        }
    }

    public interface IVodafoneGhGlPostingService
    {
        Task PostEntry(Payment message);
    }
}

using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk;
using Hubtel.Accounting.Sdk.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.Services.ProviderPostings
{
    public class HubtelGlPostingService : IHubtelGlPostingService
    {
        
        private readonly ILogger _logger;
        private readonly IErrorProducer _errorProducer;
        private readonly IAccountingApi _accountingApi;
        //private readonly IJournalEntriesApi _journalEntriesApi;

        public HubtelGlPostingService(
            
            ILogger<HubtelGlPostingService> logger,
            IErrorProducer errorProducer
            //, IJournalEntriesApi journalEntriesApi
            , IAccountingApi accountingApi
            )
        {
            
            _logger = logger;
            _errorProducer = errorProducer;
            _accountingApi = accountingApi;
            //_journalEntriesApi = journalEntriesApi;
        }

        public async Task PostEntry(Payment message)
        {
            var request = new GlRequest
            {
                CurrencyCode = "GHC",
                Comments = $"{Constants.PaymentChannels.Hubtel}",
                TimeStamp = message.PaymentDate,
                OfficeId = 2
            };

            //credit general merchant ledger
            request.Credits.Add(new GlDataModel
            {
                Amount = message.AmountAfterCharges,
                GlAccountId = 8
            });
            
            //credit provider fee ledger
            request.Credits.Add(new GlDataModel
            {
                Amount = message.Fee,
                GlAccountId = 36
            });

            //debit provider's funds ledger
            request.Debits.Add(new GlDataModel
            {
                Amount = message.AmountPaid,
                GlAccountId = 38
            });

            _logger.LogWarning($"gl request {JsonConvert.SerializeObject(request)}");

            var res = await _accountingApi.PostGl(request);

            if (!res.IsSuccessful)
            {
                await _errorProducer.Produce(new PaymentAggregateError
                {
                    Data = message,
                    Message = $"could not POST GL entries successfully for {Constants.PaymentChannels.MobileMoneyNetworks.MtnGh}. Account Number: {message.FineractSavingsAccountId} Response: {res.RawResponse}",
                    Stage = PaymentErrors.Post_Gl_type_error
                });
                _logger.LogWarning(
                    $"could not POST GL entries successfully for {Constants.PaymentChannels.MobileMoneyNetworks.MtnGh}. Account Number: {message.FineractSavingsAccountId} Response: {res.RawResponse}");
                return;
            }

            _logger.LogInformation($"successfully POSTED GL entries for account number {message.FineractSavingsAccountId} on {Constants.PaymentChannels.MobileMoneyNetworks.MtnGh}");
        }
    }

    public interface IHubtelGlPostingService
    {
        Task PostEntry(Payment message);
    }
}

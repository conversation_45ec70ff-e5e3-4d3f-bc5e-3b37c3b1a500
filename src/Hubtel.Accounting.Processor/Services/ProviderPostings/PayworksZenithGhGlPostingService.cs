using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk;
using Hubtel.Accounting.Sdk.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.Services.ProviderPostings
{
    public class PayworksZenithGhGlPostingService : IPayworksZenithGhGlPostingService
    {
        
        private readonly ILogger _logger;
        private readonly IErrorProducer _errorProducer;
        
        private readonly IAccountingApi _accountingApi;

        public PayworksZenithGhGlPostingService(
            
            ILogger<PayworksZenithGhGlPostingService> logger,
            IErrorProducer errorProducer
            
            ,IAccountingApi accountingApi)
        {
            
            _logger = logger;
            _errorProducer = errorProducer;
            
            _accountingApi = accountingApi;
        }

        public async Task PostEntry(Payment postCardPresentGhGl)
        {

            //todo: let's hold on here
            //we have a problem with rounding for lower AmountPaid values
            //accounting API rejects our GL postings
            //we have to consult and re-program
            var request = new GlRequest
            {
                CurrencyCode = "GHC",
                Comments = $"{Constants.PaymentChannels.CardConstants.CardModes.CardPresent} - {Constants.PaymentChannels.CardConstants.CardProcessors.Zenith}",
                TimeStamp = postCardPresentGhGl.PaymentDate,
                OfficeId = 2

            };

            //credit general merchant ledger
            request.Credits.Add(new GlDataModel
            {
                 Amount = (postCardPresentGhGl.AmountAfterCharges + postCardPresentGhGl.Fee) != postCardPresentGhGl.AmountPaid 
                ? postCardPresentGhGl.AmountAfterCharges - postCardPresentGhGl.Fee 
                : postCardPresentGhGl.AmountAfterCharges,
                GlAccountId = 8
            });

            //credit provider fee ledger
            request.Credits.Add(new GlDataModel
            {
                Amount = postCardPresentGhGl.Fee,
                GlAccountId = 19
            });

            //debit provider's funds ledger
            request.Debits.Add(new GlDataModel
            {
                Amount = postCardPresentGhGl.AmountPaid,
                GlAccountId = 6
            });

            _logger.LogWarning($"gl request {JsonConvert.SerializeObject(request)}");

            //var res = await _journalEntriesApi.Post(request);
            var res = await _accountingApi.PostGl(request);
            _logger.LogInformation(JsonConvert.SerializeObject(res));

            if (!res.IsSuccessful)
            {
                _logger.LogInformation("About to produce eerror message");
                await _errorProducer.Produce(new PaymentAggregateError
                {
                    Data = postCardPresentGhGl,
                    Message = $"could not POST GL entries successfully for CARD-GH. Account Number: {postCardPresentGhGl.FineractSavingsAccountId}. Response: {res.RawResponse}",
                    Stage = PaymentErrors.Post_Gl_type_error
                });
                _logger.LogWarning(
                    $"could not POST GL entries successfully for CARD-GH. Account Number: {postCardPresentGhGl.FineractSavingsAccountId}. Response: {res.RawResponse}");
                return;
            }

            
            _logger.LogInformation($"successfully POSTED GL entries for account number {postCardPresentGhGl.FineractSavingsAccountId} on CARD");
        }
    }

    public interface IPayworksZenithGhGlPostingService
    {
        Task PostEntry(Payment message);
    }
}

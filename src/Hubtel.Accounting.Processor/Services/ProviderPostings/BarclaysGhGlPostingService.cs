using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk;
using Hubtel.Accounting.Sdk.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.Services.ProviderPostings
{
    public class BarclaysGhGlPostingService : IBarclaysGhGlPostingService
    {
        
        private readonly ILogger _logger;
       // private readonly IJournalEntriesApi _journalEntriesApi;
        private readonly IErrorProducer _errorProducer;
        private readonly IAccountingApi _accountingApi;

        public BarclaysGhGlPostingService(
            
            ILogger<BarclaysGhGlPostingService> logger
            //, IJournalEntriesApi journalEntriesApi
            ,IErrorProducer errorProducer
            , IAccountingApi accountingApi)
        {
            
            _logger = logger;
           // _journalEntriesApi = journalEntriesApi;
            _errorProducer = errorProducer;
            _accountingApi = accountingApi;
        }

        public async Task PostEntry(Payment postCardNotPresentBarclaysGhGl)
        {


            //todo: let's hold on here
            //we have a problem with rounding for lower AmountPaid values
            //accounting API rejects our GL postings
            //we have to consult and re-program
            //var request = new GLPostRequest
            var request = new GlRequest
            {
                CurrencyCode = "GHC",
                Comments = $"{Constants.PaymentChannels.CardConstants.CardModes.CardNotPresent} - {Constants.PaymentChannels.CardConstants.CardProcessors.Barclays}",
                TimeStamp = postCardNotPresentBarclaysGhGl.PaymentDate,
                OfficeId = 2

            };

            //request.Credits.Add(new GLPostCreditPayload
            request.Credits.Add(new GlDataModel
            {
                Amount = postCardNotPresentBarclaysGhGl.AmountAfterCharges,
                GlAccountId = 8
            });
            //request.Credits.Add(new GLPostCreditPayload
            request.Credits.Add(new GlDataModel
            {
                Amount = postCardNotPresentBarclaysGhGl.Fee,
                GlAccountId = 37
            });

            //request.Debits.Add(new GLPostDebitPayload
            request.Debits.Add(new GlDataModel
            {
                Amount = postCardNotPresentBarclaysGhGl.AmountPaid,
                GlAccountId = 5
            });

            _logger.LogWarning($"gl request {JsonConvert.SerializeObject(request)}");
            //var res = await _journalEntriesApi.Post(request);
            var res = await _accountingApi.PostGl(request);

            if (!res.IsSuccessful)
            {
                await _errorProducer.Produce(new PaymentAggregateError
                {
                    Data = postCardNotPresentBarclaysGhGl,
                    Message = $"could not POST GL entries successfully for CARD-GH. Account Number: {postCardNotPresentBarclaysGhGl.FineractSavingsAccountId}. Response: {res.RawResponse}",
                    Stage = PaymentErrors.Post_Gl_type_error
                });
                _logger.LogWarning(
                    $"could not POST GL entries successfully for CARD-GH. Account Number: {postCardNotPresentBarclaysGhGl.FineractSavingsAccountId}. Response: {res.RawResponse}");
                return;
            }

            _logger.LogInformation($"successfully POSTED GL entries for account number {postCardNotPresentBarclaysGhGl.FineractSavingsAccountId} on CARD");
        }
    }

    public interface IBarclaysGhGlPostingService
    {
        Task PostEntry(Payment message);
    }
}

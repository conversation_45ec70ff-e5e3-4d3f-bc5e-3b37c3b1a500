using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk;
using Hubtel.Accounting.Sdk.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.Services.ProviderPostings
{
    public class GhqrGlPostingService : IGhqrGlPostingService
    {

        private readonly ILogger<GhqrGlPostingService> _logger;
        private readonly IErrorProducer _errorProducer;
        private readonly IAccountingApi _accountingApi;
        // private readonly IJournalEntriesApi _journalEntriesApi;

        public GhqrGlPostingService(
            ILogger<GhqrGlPostingService> logger,
            IErrorProducer errorProducer
            //, IJournalEntriesApi journalEntriesApi

            , IAccountingApi accountingApi)
        {

            _logger = logger;
            _errorProducer = errorProducer;
            _accountingApi = accountingApi;
            //_journalEntriesApi = journalEntriesApi;
        }

        public async Task PostEntry(Payment message)
        {
            await Task.Delay(0);
            var request = new GlRequest
            {
                CurrencyCode = "GHC",
                //Comments = $"{Constants.PaymentChannels.MobileMoneyNetworks.MtnGh}",
                Comments = string.IsNullOrEmpty(message.PaymentProcessor) ? message.MobileMoney.Network : message.PaymentProcessor,
                TimeStamp = message.PaymentDate,
                OfficeId = 2
            };

            _logger.LogDebug($"about to post GL for {message.PaymentProcessor} ({message.MobileMoney.Network})");

            request.Credits.Add(new GlDataModel
            {
                Amount = message.AmountAfterCharges,
                GlAccountId = 8
            });

            //credit provider fee ledger
            request.Credits.Add(new GlDataModel
            {
                Amount = message.Fee,
                GlAccountId = 78
            });

            //debit provider's funds ledger
            request.Debits.Add(new GlDataModel
            {
                Amount = message.AmountPaid,
                GlAccountId = 5
            });

            _logger.LogDebug($"gl request {JsonConvert.SerializeObject(request)}");

            //var res = await _journalEntriesApi.Post(request);
            var res = await _accountingApi.PostGl(request);

            if (!res.IsSuccessful)
            {
                await _errorProducer.Produce(new PaymentAggregateError
                {
                    Data = message,
                    Message = $"could not POST GL entries successfully for {Constants.PaymentChannels.Ghqr}. Account Number: {message.FineractSavingsAccountId} Response: {res.RawResponse}",
                    Stage = PaymentErrors.Post_Gl_type_error
                });
                _logger.LogWarning(
                    $"could not POST GL entries successfully for {Constants.PaymentChannels.Ghqr}. Account Number: {message.FineractSavingsAccountId} Response: {res.RawResponse}");
                return;
            }

            _logger.LogInformation($"successfully POSTED GL entries for account number {message.FineractSavingsAccountId} on {Constants.PaymentChannels.Ghqr}");
        }
    }
}
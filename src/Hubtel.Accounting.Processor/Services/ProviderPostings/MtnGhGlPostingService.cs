using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk;
using Hubtel.Accounting.Sdk.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.Services.ProviderPostings
{
    public class MtnGhGlPostingService : IMtnGhGlPostingService
    {
        
        private readonly ILogger<MtnGhGlPostingService> _logger;
        private readonly IErrorProducer _errorProducer;
       
        private readonly IAccountingApi _accountingApi;

        public MtnGhGlPostingService(
            ILogger<MtnGhGlPostingService> logger,
            IErrorProducer errorProducer
            
            , IAccountingApi accountingApi)
        {
            
            _logger = logger;
            _errorProducer = errorProducer;
           
            _accountingApi = accountingApi;
        }

        public async Task PostEntry(Payment message)
        {
            var request = new GlRequest
            {
                CurrencyCode = "GHC",
                
                Comments = string.IsNullOrEmpty(message.PaymentProcessor)?message.MobileMoney.Network:message.PaymentProcessor,
                TimeStamp = message.PaymentDate,
                OfficeId = 2
            };
           

            _logger.LogDebug($"about to post GL for {message.PaymentProcessor} ({message.MobileMoney.Network})");
            if (string.IsNullOrEmpty(message.PaymentProcessor))
            {
                request.Credits.Add(new GlDataModel
                {
                    Amount = message.AmountAfterCharges,
                    GlAccountId = 8
                });

                //credit provider fee ledger
                request.Credits.Add(new GlDataModel
                {
                    Amount = message.Fee,
                    GlAccountId = 14
                });

                //debit provider's funds ledger
                request.Debits.Add(new GlDataModel
                {
                    Amount = message.AmountPaid,
                    GlAccountId = 3
                });
            }
            else
            {
                if ("MTN GH Receive Money - Default".Equals(message.PaymentProcessor, StringComparison.OrdinalIgnoreCase))
                {
                    //credit general merchant ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.AmountAfterCharges,
                        GlAccountId = 8
                    });

                    //credit provider fee ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.Fee,
                        GlAccountId = 14
                    });

                    //debit provider's funds ledger
                    request.Debits.Add(new GlDataModel
                    {
                        Amount = message.AmountPaid,
                        GlAccountId = 3
                    });
                }
                
                
                if ("MTN GH Receive Money - Default-2".Equals(message.PaymentProcessor, StringComparison.OrdinalIgnoreCase))
                {
                    //credit general merchant ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.AmountAfterCharges,
                        GlAccountId = 8
                    });

                    //credit provider fee ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.Fee,
                        GlAccountId = 76
                    });

                    //debit provider's funds ledger
                    request.Debits.Add(new GlDataModel
                    {
                        Amount = message.AmountPaid,
                        GlAccountId = 75
                    });
                }

                if ("MTN GH Direct Debit".Equals(message.PaymentProcessor, StringComparison.OrdinalIgnoreCase))
                {
                    //credit general merchant ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.AmountAfterCharges,
                        GlAccountId = 8
                    });

                    //credit provider fee ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.Fee,
                        GlAccountId = 63
                    });

                    //debit provider's funds ledger
                    request.Debits.Add(new GlDataModel
                    {
                        Amount = message.AmountPaid,
                        GlAccountId = 61
                    });
                }

                if ("MTN GH Receive Money - Instore".Equals(message.PaymentProcessor, StringComparison.OrdinalIgnoreCase))
                {
                    //credit general merchant ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.AmountAfterCharges,
                        GlAccountId = 8
                    });

                    //credit provider fee ledger
                    request.Credits.Add(new GlDataModel
                    {
                        Amount = message.Fee,
                        GlAccountId = 65,
                        
                    });

                    //debit provider's funds ledger
                    request.Debits.Add(new GlDataModel
                    {
                        Amount = message.AmountPaid,
                        GlAccountId = 64
                    });
                }

            }



            _logger.LogDebug($"gl request {JsonConvert.SerializeObject(request)}");

            //var res = await _journalEntriesApi.Post(request);
            var res = await _accountingApi.PostGl(request);

            if (!res.IsSuccessful)
            {
                await _errorProducer.Produce(new PaymentAggregateError
                {
                    Data = message,
                    Message = $"could not POST GL entries successfully for {Constants.PaymentChannels.MobileMoneyNetworks.MtnGh}. Account Number: {message.FineractSavingsAccountId} Response: {res.RawResponse}",
                    Stage = PaymentErrors.Post_Gl_type_error
                });
                _logger.LogWarning(
                    $"could not POST GL entries successfully for {Constants.PaymentChannels.MobileMoneyNetworks.MtnGh}. Account Number: {message.FineractSavingsAccountId} Response: {res.RawResponse}");
                return;
            }

            _logger.LogInformation($"successfully POSTED GL entries for account number {message.FineractSavingsAccountId} on {Constants.PaymentChannels.MobileMoneyNetworks.MtnGh}");
        }
    }

    public interface IMtnGhGlPostingService
    {
        Task PostEntry(Payment message);
    }
}

using Akka.Actor;
using Hubtel.Accounting.Processor.ActorSys;
using Hubtel.Accounting.Processor.ActorSys.Messages;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Constants;


namespace Hubtel.Accounting.Processor
{
    class SalesSummaryService : IHostedService, IDisposable
    {
        private readonly ILogger _logger;


        private Task _executingTask;
        private readonly CancellationTokenSource _stoppingCts = new CancellationTokenSource();

        public SalesSummaryService(ILogger<SalesSummaryService> logger)
        {
            _logger = logger;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Timed Background Service is starting.");

            // Store the task we're executing
            _executingTask = ExecuteAsync(_stoppingCts.Token);

            if (_executingTask.IsCompleted)
            {
                return _executingTask;
            }

            return Task.CompletedTask;
        }

        protected async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            
                await Task.Delay(1, stoppingToken);

                AppActors.PosterActor.Tell(new StartSchedulerMessage("Start"));
         
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Timed Background Service is stopping.");

            AppActors.PosterActor.Tell(new StopSchedulerMessage("Stop"));
            if (_executingTask == null)
            {
                return;
            }

            try
            {
                // Signal cancellation to the executing method
                _stoppingCts.Cancel();
            }
            finally
            {
                // Wait until the task completes or the stop token triggers
                await Task.WhenAny(_executingTask, Task.Delay(Timeout.Infinite,
                    cancellationToken));
            }
        }

        public void Dispose()
        {
            _stoppingCts.Cancel();
        }
    }
}

using System.Collections.Generic;
using Newtonsoft.Json;

namespace Hubtel.Accounting.Processor.Config
{
    public class AccountingSettings
    {
        public AccountingSettings()
        {
            PaymentTypes = new List<AccountingPaymentType>();
            ChargeIds = new List<AccountingCharge>();
            GLFormulas = new AccountingGLChargeFormula();
            ExcludedBusinesses = new List<string>();
        }
        public string BaseUrl { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string TenantId { get; set; }
        public List<AccountingPaymentType> PaymentTypes { get; set; }
        public List<AccountingCharge> ChargeIds { get; set; }
        public AccountingGLChargeFormula GLFormulas { get; set; }
        public List<string> ExcludedBusinesses { get; set; }
    }

    public class AccountingGLChargeFormula
    {
        public List<AccountingGLChargeFormulaForConsumer> Consumer { get; set; }
        public List<AccountingGLChargeFormulaForBusiness> Business { get; set; }
        public AccountingGLChargeFormula()
        {
            Consumer = new List<AccountingGLChargeFormulaForConsumer>();
            Business = new List<AccountingGLChargeFormulaForBusiness>();
        }
    }

    public class AccountingGLChargeFormulaForConsumer: AccountingGLChargeFormulaBase
    {
 
    }
    public class AccountingGLChargeFormulaForBusiness : AccountingGLChargeFormulaBase
    {

    }

    public class AccountingGLChargeFormulaBase
    {
        public AccountingGLChargeFormulaBase()
        {
            Charges = new List<AccountingGLChargeFormulaBaseRange>();
        }
        
        public decimal PayoutPercentage { get; set; }
        public string Alias { get; set; }
        public List<AccountingGLChargeFormulaBaseRange> Charges { get; set; }
    }

    public class AccountingGLChargeFormulaBaseRange
    {
        
        public decimal Min { get; set; }
        
        public decimal Max { get; set; }
        public decimal Fixed { get; set; }
        public decimal Variable { get; set; }
    }

    public class AccountingCharge
    {
        public AccountingCharge()
        {
            Channels = new List<AccountingChargeChannel>();
        }
        public string Name { get; set; }
        public List<AccountingChargeChannel> Channels { get; set; }
    }

    public class AccountingChargeChannel
    {
        public long Id { get; set; }
        public string Name { get; set; }
    }

    public class AccountingPaymentType
    {
        public long Id { get; set; }
        public string Name { get; set; }

    }
}
using System;
using System.Collections.Generic;
using System.Text;

namespace Hubtel.Accounting.Processor.Config
{
    public class KafkaSettings
    {
        public string ConsumerGroupId { get; set; }
        public string BootstrapServers { get; set; }
        public string EnableAutoCommit { get; set; }
        public string ErrorTopic { get; set; }
        public string SettlementTopic { get; set; }
        public int PollTimeout { get; set; }
        public int BatchNumberMessages { get; set; }
        public double BatchIntervalMilliseconds { get; set; }

    }

    public class MessageGatheringInterval
    {
        public double IntervalMilliseconds { get; set; }
    }
}

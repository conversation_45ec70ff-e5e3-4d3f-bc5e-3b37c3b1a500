using Akka.Actor;
using Hubtel.Accounting.Data;
using Hubtel.Accounting.Processor.ActorSys;
using Hubtel.Accounting.Processor.ActorSys.Actors;
using Hubtel.Accounting.Processor.Config;
using Hubtel.Accounting.Processor.Services;
using Hubtel.Accounting.Processor.Services.ProviderPostings;
using JustEat.StatsD;
using Microsoft.ApplicationInsights;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Gelf.Extensions.Logging;
using Hubtel.Accounting.Data.Repository;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.Interfaces;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk.Extensions;
using Hubtel.Instrumentation.Extensions;
using Hubtel.Kafka.Host.Core;
using Hubtel.Kafka.Host.Extensions;
using Hubtel.Redis.Sdk.Extensions;
using Hubtel.Redis.Sdk.Options;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.Extensions.Options;

namespace Hubtel.Accounting.Processor
{
    class Program
    {
        private const string Appsettings = "appsettings.json";

        public static async Task Main(string[] args)
        {
            var host = new HostBuilder()
                .ConfigureHostConfiguration(configHost =>
                {
                    configHost.SetBasePath(Directory.GetCurrentDirectory());
                    configHost.AddCommandLine(args);
                })
                .ConfigureAppConfiguration((hostContext, configApp) =>
                {
                    configApp.SetBasePath(Directory.GetCurrentDirectory());
                    configApp.AddJsonFile(Appsettings, optional: false);
                    configApp.AddJsonFile(
                        $"appsettings.json",
                        optional: true);
                    configApp.AddCommandLine(args);

                })
                .ConfigureServices((hostContext, services) =>
                {
                    services.AddLogging(loggingBuilder =>
                        loggingBuilder.AddConfiguration(hostContext.Configuration.GetSection("Logging"))
                            .ClearProviders().SetMinimumLevel(LogLevel.Debug).AddConsole()

                            .AddGelf((c) =>
                            {

                                c.AdditionalFields = new Dictionary<string, object>()
                                {
                                    {"facility", hostContext.Configuration.GetSection("Logging")["GELF:Facility"]},
                                    {"Environment", hostContext.Configuration.GetSection("Logging")["GELF:Environment"]},
                                    {"machine_name", Environment.MachineName}
                                };
                                c.Host = hostContext.Configuration.GetSection("Logging")["GELF:Host"];
                                c.LogSource = hostContext.Configuration.GetSection("Logging")["GELF:LogSource"];
                                c.Port = int.Parse(hostContext.Configuration.GetSection("Logging")["GELF:Port"]);

                            }));
                    
                    services.AddSingleton(provider => new GlPostingServiceDependencies
                    {
                        MtnGhGlPostingService = provider.GetService<IMtnGhGlPostingService>(),
                        VodafoneGhGlPostingService = provider.GetService<IVodafoneGhGlPostingService>(),
                        TigoGhGlPostingService = provider.GetService<ITigoGhGlPostingService>(),
                        AirtelGhGlPostingService = provider.GetService<IAirtelGhGlPostingService>(),
                    });
                    
                    services.AddDbContext<AccountingDbContext>(options =>
                        options.UseNpgsql(hostContext.Configuration.GetSection("DatabaseSettings")["ConnectionString"],
                        o => o.MigrationsAssembly("Hubtel.Accounting.Processor")));

                    services.AddDbContext<UnifiedSalesContext>(options =>
                        options.UseNpgsql(hostContext.Configuration.GetSection("DatabaseSettings")["SalesReaderConnection"]));
                    services.AddHubtelRedisSdk(c =>
                        hostContext.Configuration.GetSection(nameof(RedisConfiguration)).Bind(c));
                    
                    services.AddApplicationInsightsTelemtryHubtel(hostContext.Configuration["ApplicationInsights:InstrumentationKey"]);
                    
                    services.Configure<KafkaProducerConfig>(hostContext.Configuration.GetSection(nameof(KafkaProducerConfig)));

                    services.AddKafkaProducerAgent(hostContext.Configuration["KafkaProducerConfig:BootstrapServers"]);
                    
                    //kafka
                    services.Configure<DatabaseSettings>(hostContext.Configuration.GetSection(nameof(DatabaseSettings)));
                    services.Configure<KafkaSettings>(hostContext.Configuration.GetSection(nameof(KafkaSettings)));
                    services.Configure<AccountingHostedServiceSetting>(hostContext.Configuration.GetSection(nameof(AccountingHostedServiceSetting)));

                    services.Configure<RedisExtra>(hostContext.Configuration.GetSection(nameof(RedisExtra)));
                    services.Configure<BusinessInfoConfig>(hostContext.Configuration.GetSection(nameof(BusinessInfoConfig)));
                    //add services
                    
                    services.AddScoped<IGlPostingService, GlPostingService>();
                    services.AddScoped<ISalesDataService, SalesDataService>();
                    services.AddScoped<IBusinessInfoService, BusinessInfoService>();
                    services.AddScoped<IPosterKafkaProducer, PosterKafkaProducer>();
                    services.AddScoped<IPaymentQueryRepository, EfPaymentQueryRepository>();


                    services.AddScoped<IAirtelGhGlPostingService, AirtelGhGlPostingService>();
                    services.AddScoped<IBarclaysGhGlPostingService, BarclaysGhGlPostingService>();
                    services.AddScoped<IEcobankGhGlPostingService, EcobankGhGlPostingService>();
                    services.AddScoped<IMtnGhGlPostingService, MtnGhGlPostingService>();
                    services.AddScoped<IPayworksZenithGhGlPostingService, PayworksZenithGhGlPostingService>();
                    services.AddScoped<ITigoGhGlPostingService, TigoGhGlPostingService>();
                    services.AddScoped<IVodafoneGhGlPostingService, VodafoneGhGlPostingService>();
                    services.AddScoped<IHubtelGlPostingService, HubtelGlPostingService>();
                    services.AddScoped<IGhqrGlPostingService, GhqrGlPostingService>();

                    services.AddSingleton(provider =>
                    {
                        var actorSys = ActorSystem.Create("Hubtel");

                        AppActors.PosterActor = actorSys.ActorOf(Props.Create(() =>
                                new PosterActor(provider.GetService<IStatsDPublisher>(),
                                    provider.GetService<ISalesDataService>(),
                                    provider.GetService<ILogger<PosterActor>>(),
                                    provider.GetService<IOptions<KafkaSettings>>(),provider.GetService<IOptions<AccountingHostedServiceSetting>>(),
                                    provider.GetService<IPosterKafkaProducer>(), provider.GetService<IBusinessInfoService>())),
                            nameof(PosterActor));
                        return actorSys;
                    });
                    //services.AddScoped(factory => new TelemetryClient
                    //{
                    //    InstrumentationKey = hostContext.Configuration["ApplicationInsights:InstrumentationKey"]
                    //});

                    services.AddScoped((p) =>
                    {
                        TelemetryConfiguration configuration = TelemetryConfiguration.CreateDefault();
                        configuration.InstrumentationKey = hostContext.Configuration["ApplicationInsights:InstrumentationKey"];
                        return new TelemetryClient(configuration);
                    });
                    
                    services.AddScoped<IErrorProducer, ErrorProducer>();
                  
                  services.AddHubtelAccountingSdk(options =>
                   {
                       options.BaseUrl = hostContext.Configuration.GetSection("AccountingSdkConfig")["BaseUrl"];
                       options.ReceiveMoneyUrl = hostContext.Configuration.GetSection("AccountingSdkConfig")["ReceiveMoneyUrl"];
                       options.Token = "";
                   });
                  
                    services.AddStatsD((provider) =>
                    {
                       return new StatsDConfiguration()
                        {
                            Host = hostContext.Configuration.GetSection("StatsD")["Host"],
                            Port = int.Parse(hostContext.Configuration.GetSection("StatsD")["Port"]),
                            Prefix = "Hubtel_Accounting_Processor",
                            OnError = ex => {
                                var ecp = ex.ToString();
                                return true;
                            }
                        };
                    });
                    //add timed service
                    services.AddHostedService<SalesSummaryService>();


                })
               
                .Build();


            var actorSystem = host.Services.GetService<ActorSystem>();
            try
            {
                var accountingDbContext = host.Services.GetService<AccountingDbContext>();
                accountingDbContext.Database.Migrate();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }

            await host.RunAsync();
        }
    }
}

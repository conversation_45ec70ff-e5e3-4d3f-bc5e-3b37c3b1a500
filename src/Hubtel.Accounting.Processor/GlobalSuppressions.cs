using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Globalization", "S1449:Culture should be specified for string operations", Justification = "EF Core database translation requires culture-invariant string operations")]
[assembly: SuppressMessage("Performance", "CA1862:Use the 'StringComparison' method overloads to perform case-insensitive string comparisons", Justification = "EF Core database translation requires ToLower() method")]
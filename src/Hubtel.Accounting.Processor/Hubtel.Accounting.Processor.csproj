<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>netcoreapp3.1</TargetFramework>
        <LangVersion>7.1</LangVersion>
        <ApplicationInsightsResourceId>/subscriptions/6caab866-07dc-47b8-997f-90123505b6d8/resourcegroups/HubtelAppInsights/providers/Microsoft.Insights/components/Hubtel.Accounting.Processor</ApplicationInsightsResourceId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Akka" Version="1.4.51" />
        <PackageReference Include="Flurl" Version="4.0.0" />
        <PackageReference Include="Flurl.Http" Version="4.0.2" />
        <PackageReference Include="Hubtel.Accounting.Sdk" Version="9.1.0" />
        <PackageReference Include="Hubtel.Redis.Sdk" Version="2.0.1" />
      <PackageReference Include="Microsoft.Extensions.DiagnosticAdapter" Version="3.1.12" />
        <PackageReference Include="Hubtel.Instrumentation" Version="1.0.15" />
        <PackageReference Include="Microsoft.ApplicationInsights" Version="2.16.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="3.1.10" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="3.1.10">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>

        <PackageReference Include="Npgsql" Version="4.1.6" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="3.1.4" />
        <PackageReference Include="Confluent.Kafka" Version="1.5.2" />

        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="3.1.10" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="3.1.32" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="3.1.10" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="3.1.10" />
       <PackageReference Include="Hubtel.Kafka.Host" Version="1.2.7" />
        <PackageReference Include="JustEat.StatsD" Version="5.0.0" />
        <PackageReference Include="Gelf.Extensions.Logging" Version="2.0.1" />

    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Hubtel.Accounting.Data\Hubtel.Accounting.Data.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>


</Project>

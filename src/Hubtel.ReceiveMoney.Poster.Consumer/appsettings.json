{"Logging": {"LogLevel": {"Default": "Debug", "System": "Information", "Microsoft": "Information"}, "GELF": {"Host": "localhost", "Port": 12202, "LogSource": "Hubtel.ReceiveMoney.Poster.Consumer", "Facility": "Hubtel.ReceiveMoney.Poster.Consumer", "Environment": "Production", "LogLevel": {"Default": "Debug"}}}, "KafkaConsumerConfig": {"BootstrapServers": "localhost:9092", "GroupId": "Hubtel.ReceiveMoney.PosterWorkers", "Topics": ["hubtel.sales.order_paid, hubtel.sales.poster.retry"], "ExtraProperties": {"auto.offset.reset": "latest"}}, "KafkaProducerConfig": {"BootstrapServers": "localhost:9092"}, "KafkaExtra": {"RetryTopic": "hubtel.sales.poster.retry", "UnprocessedTopic": "hubtel.sales.poster.unprocessed", "PosterFailedTopic": "accounting_poster_feeposting_failed"}, "ApplicationInsights": {"InstrumentationKey": "xxx"}, "BusinessInfoSdkConfig": {"BaseUrl": "https://bizinfo.hubtel.com/api", "PrivateKey": "43OUNozwz82AjZwG6BXNcBkTw4unWWES"}, "FineractCnConfig": {"BaseUrl": "https://cn.hubtel.com", "Url": "https://cn.hubtel.com/api/teller/v1/teller/1245/transactions", "TenantIdentifierCn": "playground", "Username": "<PERSON><PERSON>", "Password": "Qm9mZkA1MDUw", "Host": "https://cn.hubtel.com", "HubtelAccountIdentifier": "20116.9300.00001", "AccountsToSettle": ["11673"]}, "StatsdConfig": {"Server": "localhost", "Port": 8125, "Prefix": "statsd_prefix_here"}, "MessageGroupConsumerLogicConfig": {"TimeoutInMilliseconds": 5000, "MaxElements": 200}, "RedisConfiguration": {"Host": "127.0.0.1", "Name": "localhost", "Port": "6379", "Database": 1}}
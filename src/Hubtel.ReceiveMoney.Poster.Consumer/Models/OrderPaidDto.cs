using System;
using System.Collections.Generic;
using Hubtel.Kafka.Payload.Model;
using Newtonsoft.Json;

namespace Hubtel.ReceiveMoney.Poster.Consumer.Models
{
    public class OrderPaidDto: IKafkaMessage
    {
        public string Id { get; set; }
        public DateTime CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public string CreatedBy { get; set; }

        public string UpdatedBy { get; set; }
        public string IntegrationChannel { get; set; }

        public string PosDeviceId { get; set; }
        public string BusinessId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PosDeviceType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime OrderDate { get; set; }

        public string OrderNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Description { get; set; }

        public string Status { get; set; } //PAID, PARITALLY PAID, UNPAID, CANCELLED

        /// <summary>
        /// 
        /// </summary>
        public string AssignedTo { get; set; }

        public string EmployeeId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string EmployeeName { get; set; }

        public string CustomerMobileNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string CustomerName { get; set; }

        public string BranchId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BranchName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public float? TaxRate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public float? DiscountRate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal? DiscountAmount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal Subtotal { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal TotalAmountDue { get; set; }

        /// <summary>
        /// This is a total of all payments
        /// </summary>
        public decimal AmountPaid { get; set; }

        /// <summary>
        /// This is a total of all refunds
        /// </summary>
        public decimal AmountRefunded { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PaymentTypes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Currency { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public ICollection<OrderItem> OrderItems { get; set; }
        /// <summary>
        /// 
        /// </summary>
        ///
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public ICollection<InvoiceAddition> InvoiceAdditions { get; set; } = new List<InvoiceAddition>();
        /// <summary>
        /// 
        /// </summary>

        /// <summary>
        /// 
        /// </summary>
        public bool? IsFulfilled { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int ConsumerRating { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ConsumerFeedback { get; set; }

        public string CustomerEmail { get; set; }

        public string BusinessEmail { get; set; }

        public string BusinessMobileNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BusinessName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FcmCustomer { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FcmDevice { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal AmountDueProducer { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal DeliveryFee { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool HasDelivery { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double CustomerReward { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SenderId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string LogoUrl { get; set; }
        /// <summary>
        /// 
        /// </summary>

        public string ReturnUrl { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// [StringLength(40)]
        public string BranchEmail { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BranchPhoneNumber { get; set; }

        public string CancellationUrl { get; set; }

        /// <summary>
        /// Human readable location of a delivery, e.g. Kokomlemle
        /// </summary>
        public string DeliveryLocationName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public PaymentRequest PaymentRequest { get; set; }
        /// <summary>
        /// Indicates whether the invoice was created due to a recurring payment
        /// </summary>
        public bool IsRecurring { get; set; }
        /// <summary>
        /// RecurringInvoiceID
        /// </summary>
        public string RecurringInvoiceId { get; set; }
        /// <summary>
        /// Determines whether order is for a service so that certain services will be rendered
        /// </summary>
        public bool IsProgrammableService { get; set; }
        /// <summary>
        /// Total profit made on the sale
        /// </summary>
        public decimal TotalProfit { get; set; }

        public Payment Payment { get; set; } //will be NULL during deserializing but will be populated after
        public bool IsMultiCart { get; set; }
        public bool IsMultiCartitem { get; set; }
        public List<MultiCartItem> MultiCartIds { get; set; }
        public List<Coupon> Coupons { get; set; } = new List<Coupon>();
        public ICollection<ProducerTrace> ProducerTrace { get; set; }
    }
    
    public class MultiCartItem
    {
        public string Value { get; set; }
    }
    
    public class Coupon
    {
        public string Type { get; set; }
        public string SubType { get; set; }
        public float Unit { get; set; }

        public string CreatedBy { get; set; }

        public DateTime CreatedOn { get; set; }
        public DateTime? RedeemedOn { get; set; }
        public DateTime? OnHoldSince { get; set; }
        public DateTime? AcceptedOn { get; set; }
        public DateTime? DeletedOn { get; set; }

        public CouponState State { get; set; }

        public bool Deleted { get; set; }
        public string Id { get; set; }
        public string OrderId { get; set; }

        public long GetCouponIdAsLong()
        {
            if (!long.TryParse(Id,out var id))
            {
                return 0L;
            }

            return id;
        }
    }
    
    public class PaymentRequest
    {
        public string Channel { get; set; }
        public string CustomerMsisdn { get; set; }
        public string PrimaryCallbackUrl { get; set; }
        public string Token { get; set; }
        public string PaymentType { get; set; }
        public bool FeesOnCustomer { get; set; }
        public string BusinessId { get; set; }
        public decimal Amount { get; set; }
        public string Description { get; set; }
        public string ClientReference { get; set; }
        public string OrderId { get; set; }
        public string Currency { get; set; }
        public decimal Fee { get; set; }
        public decimal DeliveryFee { get; set; }
        public int SavingsAccountId { get; set; }
        public decimal AmountAfterCharges { get; set; }
        public string BusinessName { get; set; }
        public string ReceiptNumber { get; set; }
        public string HubtelReference { get; set; }
        public decimal AmountTendered { get; set; }
        public string CustomerMobileNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string TransactionId { get; set; } // from merchant account
        public string CustomerName { get; set; }
        public bool IsRefund { get; set; }
        public decimal Balance { get; set; }
        public string OrderNumber { get; set; }
        public DateTime OrderDate { get; set; }
        /// <summary>
        /// Indicates whether the invoice was created due to a recurring payment
        /// </summary>
        public bool IsRecurring { get; set; }
        /// <summary>
        /// RecurringInvoiceID
        /// </summary>
        public string RecurringInvoiceId { get; set; }
        /// <summary>
        /// If mobile money, this is the transaction id of the send money transaction
        /// </summary>
        public string RefundTransactionId { get; set; }
        /// <summary>
        /// The date the Business requested the refund
        /// </summary>
        public DateTime? RefundRequestedDate { get; set; }
        /// <summary>
        /// The date Hubtel completed the refund
        /// </summary>
        public DateTime? RefundCompletedDate { get; set; }
        /// <summary>
        /// Employee who requested for a Refund
        /// </summary>
        public string RefundRequestedBy { get; set; }
        /// <summary>
        /// MobileMoney or Card
        /// </summary>
        public string RefundDestinationType { get; set; }
        /// <summary>
        /// MobileMoney Number or Masked Card Number
        /// </summary>
        public string RefundDestination { get; set; }
        /// <summary>
        /// Amount that will be refunded.
        /// </summary>
        public decimal AmountRefunded { get; set; }
        /// <summary>
        /// Pending || Completed || Rejected
        /// </summary>
        public string RefundStatus { get; set; }

        public string PaymentProcessorRoute { get; set; }
        public string PaymentProcessorName { get; set; }
    }
    public class InvoiceAddition
    {
        public string Id { get; set; }
        public string BusinessId { get; set; }
        public string OrderId { get; set; }
        public string BranchId { get; set; }
        public bool IsInclusive { get; set; }
        public short Sequence { get; set; }
        public string Name { get; set; }
        public bool IsFlatFee { get; set; }
        public decimal Figure { get; set; }
        public string CalculationMethod { get; set; }
        public decimal ComputedValue { get; set; }
        public bool IsActive { get; set; }
    }
    public enum CouponState
    {
        New,
        Accepted,
        
        Used,
        OnHold
    }
}
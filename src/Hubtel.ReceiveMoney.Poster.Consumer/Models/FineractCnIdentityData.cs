using System;
using Newtonsoft.Json;

namespace Hubtel.ReceiveMoney.Poster.Consumer.Models
{
    public class FineractCnIdentityData
    {
        [JsonProperty("tokenType")]
            public string TokenType { get; set; }

            [JsonProperty("accessToken")]
            public string AccessToken { get; set; }

            [JsonProperty("accessTokenExpiration")]
            public DateTime AccessTokenExpiration { get; set; }

            [Json<PERSON>roperty("refreshTokenExpiration")]
            public DateTime RefreshTokenExpiration { get; set; }

            [JsonProperty("passwordExpiration")]
            public DateTime PasswordExpiration { get; set; }
        
    }
}
using System.Collections.Generic;
using Hubtel.Kafka.Payload.Model;

namespace Hubtel.ReceiveMoney.Poster.Consumer.Models
{
    public class TransferFailedRequest:IKafkaMessage
    {
        public string CustomerIdentifier { get; set; }

        public string ProductIdentifier { get; set; }

        public string CustomerAccountIdentifier { get; set; }

        public string TargetAccountIdentifier { get; set; }

        public string Amount { get; set; }

        public string Clerk { get; set; }

        public string TransactionDate { get; set; }

        public string TransactionType { get; set; }
        public ICollection<ProducerTrace> ProducerTrace { get; set; }
    }
}
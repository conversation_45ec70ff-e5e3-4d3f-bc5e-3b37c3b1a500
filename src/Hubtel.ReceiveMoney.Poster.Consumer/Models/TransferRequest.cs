using System;
using Newtonsoft.Json;

namespace Hubtel.ReceiveMoney.Poster.Consumer.Models
{
    public class TransferRequest
    {
        
            [JsonProperty("customerIdentifier")]
            public string CustomerIdentifier { get; set; }

            [JsonProperty("productIdentifier")]
            public string ProductIdentifier { get; set; }

            [Json<PERSON>roperty("customerAccountIdentifier")]
            public string CustomerAccountIdentifier { get; set; }

            [Json<PERSON>roperty("targetAccountIdentifier")]
            public string TargetAccountIdentifier { get; set; }

            [JsonProperty("amount")]
            public string Amount { get; set; }

            [JsonProperty("clerk")]
            public string Clerk { get; set; }

            [JsonProperty("transactionDate")]
            public string TransactionDate { get; set; }

            [JsonProperty("transactionType")]
            public string TransactionType { get; set; }
        
    }
}
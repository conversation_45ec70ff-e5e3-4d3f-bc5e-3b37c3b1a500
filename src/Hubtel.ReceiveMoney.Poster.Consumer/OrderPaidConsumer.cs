using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.DI.Core;
using Hubtel.BusinessInfo.Sdk.Models;
using Hubtel.Kafka.Host.Core;
using Hubtel.ReceiveMoney.Poster.Consumer.Components.Actors;
using Hubtel.ReceiveMoney.Poster.Consumer.Configs;
using Hubtel.ReceiveMoney.Poster.Consumer.Interfaces;
using Hubtel.ReceiveMoney.Poster.Consumer.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hubtel.ReceiveMoney.Poster.Consumer
{
    public class OrderPaidConsumer : KafkaConsumerBase
    {
        private readonly ILogger<OrderPaidConsumer> _logger;
        private readonly IBizInfoService _bizInfoService;
        private readonly ActorSystem _actorSystem;
        private readonly FineractCnConfig _fineractCnConfig;

        private readonly KafkaExtra _kafkaExtra;


        public OrderPaidConsumer(ILogger<OrderPaidConsumer> logger,
            IOptions<KafkaExtra> kafkaExtra,
            IOptions<FineractCnConfig> fineractCnConfig
            ,IBizInfoService bizInfoService
            ,ActorSystem actorSystem
        )
        {
            _logger = logger;
            _bizInfoService = bizInfoService;
            _actorSystem = actorSystem;
            _fineractCnConfig = fineractCnConfig.Value;

            _kafkaExtra = kafkaExtra.Value;
        }

        //uncomment this for bulk read
        [ConsumeTopic(FromType = typeof(IOptions<KafkaConsumerConfig>),
            PropertyName = nameof(KafkaConsumerConfig.TopicsAsSingleString))]
        public async Task HandleBulkMessage(List<OrderPaidDto> messages)
        {
            var accounts = _fineractCnConfig.AccountsToSettle.Distinct().ToList();

            var accountsToSettle = messages;
            if (!accounts.Contains("*"))
            {
                accountsToSettle = messages.Where(x => accounts.Contains(x.Payment.FineractSavingsAccountId.ToString())).ToList();

            }

            
            var accountGroup = accountsToSettle.GroupBy(x => x.Payment.FineractSavingsAccountId);

            foreach (var batch in accountGroup)
            {
                //fetch details from redis through a service....
                if (!batch.Any())
                {
                    continue;
                }
                var businessId = batch.FirstOrDefault()?.BusinessId;
                var fineractAccountDetails = await _bizInfoService.GetFineractCnInfo(businessId, batch.Key.ToString());
                
                
                if (fineractAccountDetails == null)
                {
                    continue;
                }
                //send batch messages + account details to an actor (created on  the fly)
                
                var actor = _actorSystem.ActorOf(_actorSystem.DI()
                        .Props<MainActor>()
                        .WithSupervisorStrategy(TopLevelActors.GetDefaultSupervisorStrategy)
                    , $"{nameof(MainActor)}-{batch.Key}-{Guid.NewGuid().ToString("N")}");
                
                actor.Tell(new HandleBatchPostings(fineractAccountDetails,batch.ToList()));

            }
            // if (accountsToSettle.Any())
            // {
            //     messages.ForEach(m => TopLevelActors.MainActor.Tell(new ProcessMyModel()));
            // }

            await Task.Delay(0);
        }


      


        public override Task ConsumingStopped()
        {
            _logger.LogWarning($"consumer stopped at {DateTime.UtcNow}");
            return Task.CompletedTask;
        }
    }

    public struct HandleBatchPostings
    {
        public GetBranchInfoWithBusinessResp FineractAccountDetails { get; }
        public List<OrderPaidDto> OrderPaidDtos { get; }

        public HandleBatchPostings(GetBranchInfoWithBusinessResp fineractAccountDetails, List<OrderPaidDto> orderPaidDtos)
        {
            FineractAccountDetails = fineractAccountDetails;
            OrderPaidDtos = orderPaidDtos;
        }
    }
}
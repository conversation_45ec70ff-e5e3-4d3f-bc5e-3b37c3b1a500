using System.Threading.Tasks;
using Hubtel.BusinessInfo.Sdk.Models;
using Hubtel.ReceiveMoney.Poster.Consumer.Models;

namespace Hubtel.ReceiveMoney.Poster.Consumer.Interfaces
{
    public interface IBizInfoService
    {
        //Task<GetBranchInfoWithBusinessResp> GetFineractCnInfo(OrderPaidDto message);
        Task<GetBranchInfoWithBusinessResp> GetFineractCnInfo(string businessId,string fineractSavingsId);
    }
}
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Gelf.Extensions.Logging;
using Hubtel.BusinessInfo.Sdk.Extensions;
using Hubtel.Instrumentation.Extensions;
using Hubtel.Kafka.Host;
using Hubtel.Kafka.Host.Core;
using Hubtel.Kafka.Host.Core.ConsumerLogic;
using Hubtel.Kafka.Host.Extensions;
using Hubtel.ReceiveMoney.Poster.Consumer.Configs;
using Hubtel.ReceiveMoney.Poster.Consumer.Extensions;
using Hubtel.ReceiveMoney.Poster.Consumer.Interfaces;
using Hubtel.ReceiveMoney.Poster.Consumer.Models;
using Hubtel.ReceiveMoney.Poster.Consumer.Services;
using JustEat.StatsD;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Hubtel.ReceiveMoney.Poster.Consumer
{
    class Program
    {
        public static async Task Main(string[] args)
        {
            var host = CreateKafkaBuilder(args).Build();
            await host.RunAsync();
        }

        public static IHostBuilder CreateKafkaBuilder(string[] args) =>
            KafkaHost.CreateDefaultBuilder(args)
                .ConfigureServices((hostContext, services) =>
                {
                    //mandatory
                    services.Configure<KafkaConsumerConfig>(options =>
                        hostContext.Configuration.GetSection(nameof(KafkaConsumerConfig)).Bind(options));
                    services.Configure<KafkaProducerConfig>(options =>
                        hostContext.Configuration.GetSection(nameof(KafkaProducerConfig)).Bind(options));

                    services.Configure<KafkaExtra>(options =>
                        hostContext.Configuration.GetSection(nameof(KafkaExtra)).Bind(options));
                    services.Configure<FineractCnConfig>(options =>
                        hostContext.Configuration.GetSection(nameof(FineractCnConfig)).Bind(options));

                    services.AddSingleton<IBizInfoService, BizInfoService>();
                    services.AddSingleton<IFineractCnService, FineractCnService>();

                    //uncomment these lines for bulk-consume
                    services.Configure<MessageGroupConsumerLogicConfig>(options => hostContext.Configuration.GetSection(nameof(MessageGroupConsumerLogicConfig)).Bind(options));
                     services.AddSingleton<IKafkaConsumerLogicBase, MessageGroupConsumerLogic<OrderPaidDto>>();
                     
                     services.AddHubtelBusinessInfoSdk(options =>
                     {
                         options.BaseUrl = hostContext.Configuration["BusinessInfoSdkConfig:BaseUrl"];
                         options.PrivateKey = hostContext.Configuration["BusinessInfoSdkConfig:PrivateKey"];
                     });

                    //AI
                    services.AddApplicationInsightsTelemetryWorkerService(
                        hostContext.Configuration["ApplicationInsights:InstrumentationKey"]);
                    services.AddApplicationInsightsTelemtryHubtel(
                        hostContext.Configuration["ApplicationInsights:InstrumentationKey"]);

                    services.AddKafkaProducerAgent(hostContext.Configuration["KafkaProducerConfig:BootstrapServers"]);


                    //rest of service registry
                    services.AddLogging(loggingBuilder =>
                        loggingBuilder.AddConfiguration(hostContext.Configuration.GetSection("Logging"))
                            .ClearProviders().SetMinimumLevel(LogLevel.Debug).AddConsole()
                            .AddGelf((c) =>
                            {
                                c.AdditionalFields = new Dictionary<string, object>()
                                {
                                    {"facility", hostContext.Configuration.GetSection("Logging")["GELF:Facility"]},
                                    {
                                        "Environment",
                                        hostContext.Configuration.GetSection("Logging")["GELF:Environment"]
                                    },
                                    {"machine_name", Environment.MachineName}
                                };
                                c.Host = hostContext.Configuration.GetSection("Logging")["GELF:Host"];
                                c.LogSource = hostContext.Configuration.GetSection("Logging")["GELF:LogSource"];
                                c.Port = int.Parse(hostContext.Configuration.GetSection("Logging")["GELF:Port"]);
                            }));


                    services.AddStatsD(
                        (provider) =>
                        {
                            var logger = provider.GetService<ILogger<Program>>();
                            return new StatsDConfiguration()
                            {
                                Host = hostContext.Configuration.GetSection("StatsdConfig")["Server"],
                                Port = int.Parse(hostContext.Configuration.GetSection("StatsdConfig")["Port"]),
                                Prefix = hostContext.Configuration.GetSection("StatsdConfig")["Prefix"],

                                OnError = (ex) =>
                                {
                                    logger?.LogError(ex, ex.Message);
                                    return true;
                                }
                            };
                        });

                    services.AddSingleton<IBizInfoService, BizInfoService>();
                    services.AddSingleton<IFineractCnService, FineractCnService>();
                    services.AddRedisStorage(new RedisConfiguration
                    {
                        Database = hostContext.Configuration.GetValue<int>("RedisConfiguration:Database"),
                        Host = hostContext.Configuration.GetValue<string>("RedisConfiguration:Host"),
                        Port = hostContext.Configuration.GetValue<string>("RedisConfiguration:Port")
                    });
                    
              

                    services.AddActorSystem("FineractCNAccountingPosterActorSystem");
                });
    }
}
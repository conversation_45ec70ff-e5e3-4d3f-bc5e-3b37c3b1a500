using System;
using Akka.Actor;
using Akka.DI.AutoFac;
using Akka.DI.Core;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Hubtel.ReceiveMoney.Poster.Consumer.Components.Actors;
using Hubtel.ReceiveMoney.Poster.Consumer.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;

namespace Hubtel.ReceiveMoney.Poster.Consumer.Extensions
{
    public static class ServiceCollectionExtension
    {
        public static IServiceCollection AddActorSystem(this IServiceCollection services, string actorSystemName)
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }


            var actorSystem = ActorSystem.Create(actorSystemName);
            services.AddSingleton(typeof(ActorSystem), sp => actorSystem);

            var builder = new ContainerBuilder();
            builder.Populate(services);

            builder.RegisterType<MainActor>();


            var container = builder.Build();

            var resolver = new AutoFacDependencyResolver(container, actorSystem);

            TopLevelActors.ActorSystem = actorSystem;
            TopLevelActors.MainActor = actorSystem.ActorOf(actorSystem.DI()
                    .Props<MainActor>()
                    .WithSupervisorStrategy(TopLevelActors.GetDefaultSupervisorStrategy)
                , nameof(MainActor));
            


            return services;
        }
        
        
        public static IServiceCollection AddRedisStorage(this IServiceCollection services, RedisConfiguration configureOptions)
        {
            if (services == null)
            {
                throw new ArgumentNullException(nameof(services));
            }

            services.Configure<RedisConfiguration>(c =>
            {
                c.Port = configureOptions.Port;
                c.Database = configureOptions.Database;
                c.Host = configureOptions.Host;
            });
            var configuration = ConfigurationOptions.Parse($"{configureOptions.Host}:{configureOptions.Port}", true);
            configuration.ResolveDns = false;
            configuration.AbortOnConnectFail = false;
            configuration.AllowAdmin = true;
            configuration.DefaultDatabase = configureOptions.Database;
            configuration.ReconnectRetryPolicy = new LinearRetry(500);
           

            var connection = ConnectionMultiplexer.Connect(configuration);
            var db = connection.GetDatabase();

            services.AddSingleton<IRedisCacheRepository>((sp) =>
                new RedisCacheRepository(db, sp.GetService<ILogger<RedisCacheRepository>>()));
            
            return services;
        }
    }
}
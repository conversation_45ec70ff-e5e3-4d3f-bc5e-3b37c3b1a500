using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;
using Order = StackExchange.Redis.Order;

namespace Hubtel.ReceiveMoney.Poster.Consumer.Services
{
    public class RedisCacheRepository : IRedisCacheRepository
    {
        private readonly ILogger<RedisCacheRepository> _logger;

        public static string RedisKey = $"hubtel:redis-api";
        public RedisCacheRepository(IDatabase database, ILogger<RedisCacheRepository> logger)
        {
            _logger = logger;
            Database = database;
        }


        public IDatabase Database { get; set; }


        public async Task<bool> AddToSortedSet<T>(string key, long score, T model)
        {
            var result = await Database.SortedSetAddAsync(
                key, JsonConvert.SerializeObject(model), score
            );

            _logger.LogDebug($"Added to sorted set => {key}");
            return result;
        }

    
        public async Task<long> RemoveFromSortedSet(string key, double score)
        {
            var result = await Database.SortedSetRemoveRangeByScoreAsync(
                key, score, score
            );

            _logger.LogDebug($"Removed from sorted set => {key}");
            return result;
        }

        public async Task<IEnumerable<T>> FindInSortedSet<T>(string key, long startScore, long endScore)
        {
            var results = (
                await Database.SortedSetRangeByScoreAsync(key,
                    order: Order.Descending, start: startScore, stop: endScore)
            ).Select(value => JsonConvert.DeserializeObject<T>(value));

            _logger.LogDebug($"Retrieve items for key => {key}");

            return results;
        }

        public async Task<IEnumerable<T>> FetchFromSortedSet<T>(string key, Order order = Order.Descending)
        {
            var results = (
                await Database.SortedSetRangeByRankAsync(key,
                    order: order)
            ).Select(value => JsonConvert.DeserializeObject<T>(value));

            _logger.LogDebug($"Retrieve items for key => {key}");

            return results;
        }

        public async Task<IEnumerable<T>> FetchFromSortedSet<T>(string key, int page, int size,
            Order order = Order.Descending)
        {
            var start = size * Math.Max(page - 1, 0);

            var results = (
                await Database.SortedSetRangeByRankAsync(
                    key,
                    order: order,
                    start: start,
                    stop: start + size - 1
                )
            ).Select(value => JsonConvert.DeserializeObject<T>(value));

            _logger.LogDebug($"Retrieve items for key => {key}");

            return results;
        }

        public async Task<bool> SetKey<T>(string key, T model)
        {
            return await Database.StringSetAsync(key, JsonConvert.SerializeObject(model));
        }

        public async Task<T> GetKey<T>(string key)
        {
            return JsonConvert.DeserializeObject<T>(await Database.StringGetAsync(key));
        }

        public async Task<long> SortedSetCardinality(string key)
        {
            return await Database.SortedSetLengthAsync(key);
        }
    }
}
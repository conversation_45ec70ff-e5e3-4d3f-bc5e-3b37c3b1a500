using System;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Flurl.Http;
using Hubtel.Kafka.Host.Core;
using Hubtel.ReceiveMoney.Poster.Consumer.Configs;
using Hubtel.ReceiveMoney.Poster.Consumer.Interfaces;
using Hubtel.ReceiveMoney.Poster.Consumer.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.ReceiveMoney.Poster.Consumer.Services
{
    public class FineractCnService: IFineractCnService
    {
        private readonly ILogger<FineractCnService> _logger;
        private readonly FineractCnConfig _fineractCnConfig;
        private readonly IRedisCacheRepository _cacheManager;
        private readonly IKafkaProducerService _producerService;
        private readonly IOptions<KafkaExtra> _kakfaOptions;

        public FineractCnService(ILogger<FineractCnService> logger, IOptions<FineractCnConfig> fineractCnConfig,
            IRedisCacheRepository cacheManager, IKafkaProducerService producerService, IOptions<KafkaExtra> kakfaOptions)
        {
            _logger = logger;
            _cacheManager = cacheManager;
            _producerService = producerService;
            _kakfaOptions = kakfaOptions;
            _fineractCnConfig = fineractCnConfig.Value;
        }

        public async Task<FineractCnIdentityData> IdentityLogin()
        {
            var url = $"{_fineractCnConfig.BaseUrl}/identity/v1/token";

            HttpResponseMessage httpResp;


            try
            {
                httpResp = await url.WithHeaders(new
                {
                    X_Tenant_Identifier = _fineractCnConfig.TenantIdentifierCn,
                }).SetQueryParams(new
                {
                    grant_type = "password",
                    username = _fineractCnConfig.Username,
                    password = _fineractCnConfig.Password
                }).PostAsync(null);
            }
            catch (Exception e)
            {
                _logger.LogError(e, e.ToString());
                return null;
            }


            var rawResponse = await httpResp.Content.ReadAsStringAsync();

            var resp = JsonConvert.DeserializeObject<FineractCnIdentityData>(rawResponse);

            return resp;
        }

        private async Task<string> LoginCache()
        {
            try
            {
                var cacheVal = await _cacheManager.Database.StringGetAsync("hubtel:fineractCnIdentityLogin");

                if (!string.IsNullOrEmpty(cacheVal))
                    return cacheVal;

                var loginResp = await IdentityLogin();


                cacheVal = $"{loginResp.AccessToken}";

                await _cacheManager.Database.StringSetAsync("hubtel:fineractCnIdentityLogin", cacheVal, TimeSpan.FromMinutes(20));

                return cacheVal;
            }
            catch (Exception e)
            {
                return null;
            }
        }


        public async Task<TrnxData> MakeDepositToAccount(FineractCnDepositRequest request)
        {
            var url = $"{_fineractCnConfig.Host}/api/teller/v1/teller/1245/transactions";

            var bearerToken = await LoginCache();

            HttpResponseMessage httpResp;
            request.TransactionType = "CDPT";
            request.Clerk = "Hubtel";
            
            _logger.LogInformation($"request: {JsonConvert.SerializeObject(request)}");
            HttpResponseMessage servResp;
            try
            {
                _logger.LogInformation($"Sending Request to {_fineractCnConfig.Url}");

                servResp = await url.WithHeaders(new
                    {
                        X_Tenant_Identifier = _fineractCnConfig.TenantIdentifierCn,
                        Authorization = bearerToken,
                        User = _fineractCnConfig.Username
                    })
                    .AllowAnyHttpStatus().PostJsonAsync(request);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occured ");
                return new TrnxData
                {
                    Code = 400,
                    IsSuccess = false
                };
            }

            var raw = await servResp.Content.ReadAsStringAsync();
            _logger.LogInformation($"Raw Response from deposit transaction: {raw}");
            if (servResp.IsSuccessStatusCode)
            {
                var resp = JsonConvert.DeserializeObject<TransactionResponse>(raw);

                await ConfirmTransaction(resp.TellerTransactionIdentifier);
                return new TrnxData
                {
                    IsSuccess = true,
                    Code = (int) servResp.StatusCode
                };
            }

            return new TrnxData
            {
                IsSuccess = false,
                Code = (int) servResp.StatusCode
            };
        }

        public async Task ConfirmTransaction(string transactionIdentifier)
        {
            var url = $"{_fineractCnConfig.Url}/{transactionIdentifier}?command=CONFIRM&charges=included";
            
            _logger.LogInformation($"Confirm Transaction Url: {url}");
            
            var bearerToken = await LoginCache();

            HttpResponseMessage servResp;
            try
            {
                _logger.LogInformation($"Sending Request to {_fineractCnConfig.Url}");

                servResp = await url.WithHeaders(new
                    {
                        X_Tenant_Identifier = _fineractCnConfig.TenantIdentifierCn,
                        Authorization = bearerToken,
                        User = _fineractCnConfig.Username
                    })
                    .AllowAnyHttpStatus().PostJsonAsync(null);
            }
            catch (Exception e)
            {
                _logger.LogError(e, $"Error occured for trasactionId: {transactionIdentifier}");
                return;
            }

            var raw = await servResp.Content.ReadAsStringAsync();
            
            _logger.LogInformation($"Response returned Status code: {(int) servResp.StatusCode}");
            
            if (servResp.IsSuccessStatusCode)
            {
                _logger.LogInformation($"Confirm transaction for {transactionIdentifier} successful");
            }
        }

        public async Task TransferToAccount(TransferRequest request)
        {
            var url = $"{_fineractCnConfig.Host}/api/teller/v1/teller/1245/transactions";

            var bearerToken = await LoginCache();

            request.TransactionType = "ACCT";
            request.Clerk = "Hubtel";
            
            _logger.LogInformation($"Transfer request: {JsonConvert.SerializeObject(request)}");
            HttpResponseMessage servResp;
            try
            {
                _logger.LogInformation($"Sending Request to {_fineractCnConfig.Url}");

                servResp = await url.WithHeaders(new
                    {
                        X_Tenant_Identifier = _fineractCnConfig.TenantIdentifierCn,
                        Authorization = bearerToken,
                        User = _fineractCnConfig.Username
                    })
                    .AllowAnyHttpStatus().PostJsonAsync(request);
            }
            catch (Exception e)
            {
                _logger.LogError(e, $"Error occured for request: {JsonConvert.SerializeObject(request)}");
                return;
            }

            var raw = await servResp.Content.ReadAsStringAsync();
            _logger.LogInformation($"Raw Response from {request.CustomerAccountIdentifier} transfer to {request.TargetAccountIdentifier} transaction: {raw}");
            if (servResp.IsSuccessStatusCode)
            {
                var resp = JsonConvert.DeserializeObject<TransactionResponse>(raw);

                await ConfirmTransaction(resp.TellerTransactionIdentifier);
                
                return;
            }
            
            _logger.LogInformation($"Transfer request failed for request: {JsonConvert.SerializeObject(request)} with code {(int) servResp.StatusCode}");

            var payload = new TransferFailedRequest
            {
                Amount = request.Amount,
                Clerk = request.Clerk,
                CustomerAccountIdentifier = request.CustomerAccountIdentifier,
                CustomerIdentifier = request.CustomerIdentifier,
                ProductIdentifier = request.ProductIdentifier,
                TargetAccountIdentifier = request.TargetAccountIdentifier,
                TransactionDate = request.TransactionDate,
                TransactionType = request.TransactionType
            };

            _logger.LogInformation("produced to fee failed topic");
            await _producerService.Produce(_kakfaOptions.Value.PosterFailedTopic, payload);
        }
    }
}
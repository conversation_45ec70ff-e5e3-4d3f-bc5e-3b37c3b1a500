using System;
using System.Threading.Tasks;
using Hubtel.BusinessInfo.Sdk.Models;
using Hubtel.BusinessInfo.Sdk.Services.Backoffice;
using Hubtel.ReceiveMoney.Poster.Consumer.Interfaces;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Hubtel.ReceiveMoney.Poster.Consumer.Services
{
    public class BizInfoService: IBizInfoService
    {
        private readonly ILogger<BizInfoService> _logger;
        private readonly IBackofficeApi _backofficeApi;
        private readonly IRedisCacheRepository _cacheManager;

        public BizInfoService(ILogger<BizInfoService> logger, IBackofficeApi backofficeApi, IRedisCacheRepository cacheManager)
        {
            _logger = logger;
            _backofficeApi = backofficeApi;
            _cacheManager = cacheManager;
        }

        public async Task<GetBranchInfoWithBusinessResp> GetFineractCnInfo(string businessId,string fineractSavingsId)
        {
            try
            {
                var fineractBranchInfo =
                    await _cacheManager.Database.StringGetAsync(
                        $"hubtel:bizinfo:{businessId}:{fineractSavingsId}");

                if (!string.IsNullOrEmpty(fineractBranchInfo))
                {
                    return JsonConvert.DeserializeObject<GetBranchInfoWithBusinessResp>(fineractBranchInfo);
                }
            
                var data = await _backofficeApi.GetBranchFineractAccountInfoWithSavingsId(fineractSavingsId);

                if (data.IsSuccessful)
                {
                    await _cacheManager.Database.StringSetAsync(
                        $"hubtel:bizinfo:{businessId}:{fineractSavingsId}",
                        JsonConvert.SerializeObject(data),TimeSpan.FromHours(1));

                    return data;
                }

                return null;
            }
            catch (Exception e)
            {
                _logger.LogError(e, e.ToString());
                throw;
            }
           
        }
    }
}
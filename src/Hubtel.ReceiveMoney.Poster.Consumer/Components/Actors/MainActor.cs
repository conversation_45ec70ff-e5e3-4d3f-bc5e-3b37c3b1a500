using System;
using System.Threading.Tasks;
using Akka.Actor;
using Hubtel.Kafka.Host.Core;
using Hubtel.ReceiveMoney.Poster.Consumer.Components.Actors.ActorMessages;
using Hubtel.ReceiveMoney.Poster.Consumer.Configs;
using Hubtel.ReceiveMoney.Poster.Consumer.Interfaces;
using Hubtel.ReceiveMoney.Poster.Consumer.Models;
using Hubtel.ReceiveMoney.Poster.Consumer.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hubtel.ReceiveMoney.Poster.Consumer.Components.Actors
{
    public class MainActor : BaseActor
    {
        private readonly IRedisCacheRepository _redisCache;
        private readonly IFineractCnService _fineractCnService;
        private readonly IBizInfoService _bizInfoService;
        private readonly ILogger<MainActor> _logger;
        private readonly IKafkaProducerService _producerService;
        private readonly IOptions<KafkaExtra> _kafkaOptions;
        private readonly IOptions<FineractCnConfig> _fineractConfig;

        public MainActor(IRedisCacheRepository redisCache
            , IFineractCnService fineractCnService
            , IBizInfoService bizInfoService
            
            ,ILogger<MainActor> logger, IKafkaProducerService producerService, IOptions<KafkaExtra> kafkaOptions, IOptions<FineractCnConfig> fineractConfig)
        {
            _redisCache = redisCache;
            _fineractCnService = fineractCnService;
            _bizInfoService = bizInfoService;
            _logger = logger;
            _producerService = producerService;
            _kafkaOptions = kafkaOptions;
            _fineractConfig = fineractConfig;

            ReceiveAsync<ProcessMyModel>(DoProcessMyModel);
            ReceiveAsync<HandleBatchPostings>(DoHandleBatchPostings);
        }

        private async Task DoHandleBatchPostings(HandleBatchPostings message)
        {
            message.OrderPaidDtos.ForEach(m=>Self.Tell(new ProcessMyModel(m,message.FineractAccountDetails)));
            await Task.Delay(0);
        }

        private async Task DoProcessMyModel(ProcessMyModel message)
        {

            var key = $"hubtel:receivemoney:poster:processed:{DateTime.UtcNow:ddMMyyyy}";
            if (await _redisCache.Database.SetContainsAsync(key,message.Model.Id))
            {
                _logger.LogInformation($"orderId: {message.Model.Id} has already been processed");
                return;
            }
            var depositRequest = new FineractCnDepositRequest
            {
                Amount = message.Model.AmountPaid.ToString("N2"),
                CustomerAccountIdentifier = message.BranchInfo.Data.CustomerAccountIdentifier,
                ProductIdentifier = message.BranchInfo.Data.ProductIdentifier,
                TransactionDate = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:sssZ"),
                CustomerIdentifier = message.BranchInfo.Data.CustomerIdentifier,
            };
            
            var fineractCn = await _fineractCnService.MakeDepositToAccount(depositRequest);
            
            _logger.LogInformation($"response from deposit: {JsonConvert.SerializeObject(fineractCn)}");

            if (fineractCn.IsSuccess)
            {
                _logger.LogInformation("Deposit successful, will continue to transfer fees");
                //success
                var res = await _redisCache.Database.SetAddAsync(key, message.Model.Id);
                var request = new TransferRequest
                {
                    Amount = message.Model.Payment.Charges.ToString(),
                    CustomerIdentifier = message.BranchInfo.Data.CustomerAccountIdentifier,
                    CustomerAccountIdentifier = message.BranchInfo.Data.CustomerAccountIdentifier,
                    ProductIdentifier = message.BranchInfo.Data.ProductIdentifier,
                    TargetAccountIdentifier = _fineractConfig.Value.HubtelAccountIdentifier,
                    TransactionDate = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:sssZ"),
                };

                await _fineractCnService.TransferToAccount(request);
            }
            else if(fineractCn.Code == 403)
            {
                _logger.LogInformation($"returned unauthorised, produced to {_kafkaOptions.Value.RetryTopic} with orderId: {message.Model.Id}");
                await _producerService.Produce(_kafkaOptions.Value.RetryTopic, message.Model);
            }
            else
            {
                _logger.LogInformation($"produced to {_kafkaOptions.Value.UnprocessedTopic} with orderId: {message.Model.Id}");
                await _producerService.Produce(_kafkaOptions.Value.UnprocessedTopic, message.Model);
            }


        
        }
    }
}
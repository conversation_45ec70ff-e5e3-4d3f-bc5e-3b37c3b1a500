using Hubtel.BusinessInfo.Sdk.Models;
using Hubtel.ReceiveMoney.Poster.Consumer.Models;

namespace Hubtel.ReceiveMoney.Poster.Consumer.Components.Actors.ActorMessages
{
    public struct ProcessMyModel
    {
        public OrderPaidDto Model { get; }
        public GetBranchInfoWithBusinessResp BranchInfo { get; }

        public ProcessMyModel(OrderPaidDto model, GetBranchInfoWithBusinessResp branchInfo)
        {
            Model = model;
            BranchInfo = branchInfo;
        }
    }
}

Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.28803.202
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{D07277E9-28C4-4052-9F9D-6C4875B29814}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{D6464770-5668-48D3-B4E9-8A27119F8C8B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Hubtel.Accounting.Data", "src\Hubtel.Accounting.Data\Hubtel.Accounting.Data.csproj", "{4A2FDB9C-0F76-4E95-A269-68527322CC47}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Hubtel.Accounting.Processor", "src\Hubtel.Accounting.Processor\Hubtel.Accounting.Processor.csproj", "{B157F3EC-5E3E-44F2-8CA7-A4F54D9649F3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Hubtel.Accounting.Processor.Tests", "tests\XUnitTestProject1\Hubtel.Accounting.Processor.Tests.csproj", "{********-AA5F-4429-863E-477613A32826}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Hubtel.ReceiveMoney.Poster.Consumer", "src\Hubtel.ReceiveMoney.Poster.Consumer\Hubtel.ReceiveMoney.Poster.Consumer.csproj", "{F5965D7B-6CF1-4495-8CA0-33F16F1FD4F9}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4A2FDB9C-0F76-4E95-A269-68527322CC47}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4A2FDB9C-0F76-4E95-A269-68527322CC47}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4A2FDB9C-0F76-4E95-A269-68527322CC47}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4A2FDB9C-0F76-4E95-A269-68527322CC47}.Release|Any CPU.Build.0 = Release|Any CPU
		{B157F3EC-5E3E-44F2-8CA7-A4F54D9649F3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B157F3EC-5E3E-44F2-8CA7-A4F54D9649F3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B157F3EC-5E3E-44F2-8CA7-A4F54D9649F3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B157F3EC-5E3E-44F2-8CA7-A4F54D9649F3}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-AA5F-4429-863E-477613A32826}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-AA5F-4429-863E-477613A32826}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-AA5F-4429-863E-477613A32826}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-AA5F-4429-863E-477613A32826}.Release|Any CPU.Build.0 = Release|Any CPU
		{F5965D7B-6CF1-4495-8CA0-33F16F1FD4F9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F5965D7B-6CF1-4495-8CA0-33F16F1FD4F9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F5965D7B-6CF1-4495-8CA0-33F16F1FD4F9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F5965D7B-6CF1-4495-8CA0-33F16F1FD4F9}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{4A2FDB9C-0F76-4E95-A269-68527322CC47} = {D07277E9-28C4-4052-9F9D-6C4875B29814}
		{B157F3EC-5E3E-44F2-8CA7-A4F54D9649F3} = {D07277E9-28C4-4052-9F9D-6C4875B29814}
		{********-AA5F-4429-863E-477613A32826} = {D6464770-5668-48D3-B4E9-8A27119F8C8B}
		{F5965D7B-6CF1-4495-8CA0-33F16F1FD4F9} = {D07277E9-28C4-4052-9F9D-6C4875B29814}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {40A13719-3F47-4104-BE30-D9DB1F8BBBC9}
	EndGlobalSection
EndGlobal

using System.IO;
using Microsoft.Extensions.Configuration;

namespace Hubtel.Accounting.Processor.Tests
{
    public class ConfigurationManager
    {
        public static void Init()
        {
            var builder = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json",
                true,
                true).AddJsonFile($"appsettings.json",
                true).AddEnvironmentVariables();

            Configuration = builder.Build();
        }
        public static IConfiguration Configuration { get; private set; }
    }
}
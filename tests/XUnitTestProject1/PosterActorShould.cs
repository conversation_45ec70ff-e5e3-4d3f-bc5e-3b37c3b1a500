using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Akka.Actor;
using Akka.TestKit.Xunit2;
using Hubtel.Accounting.Processor.ActorSys.Actors;
using Hubtel.Accounting.Processor.Config;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services;
using Hubtel.Accounting.Processor.Services.Interfaces;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Kafka.Host.Core;
using JustEat.StatsD;
using Microsoft.ApplicationInsights;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using Xunit;

namespace Hubtel.Accounting.Processor.Tests
{
    public class PosterActorShould : TestKit, IClassFixture<DiFixture>
    {

        private IActorRef _posterActor = ActorRefs.Nobody;
        
        private IServiceProvider _serviceProvider;
        private readonly DiFixture _fixture;

     
        public PosterActorShould(DiFixture fixture)
        {
            _fixture = fixture;

            _serviceProvider = fixture.ServiceProvider;
          
            ConfigureDi();
        }

        private static SupervisorStrategy GetDefaultSupervisorStrategy => new OneForOneStrategy(3,
            TimeSpan.FromSeconds(3),
            ex =>
            {
                if (ex is ActorInitializationException)
                {
                    return Directive.Stop;
                }

                return Directive.Resume;
            });

        private void ConfigureDi()
        {
            var depositService = _serviceProvider.GetService<IDepositService>();
            var statsDPublisher = _serviceProvider.GetService<IStatsDPublisher>();
            var salesDataService = _serviceProvider.GetService<ISalesDataService>();
            var logger = _serviceProvider.GetService<ILogger<PosterActor>>();
            var telemetry = _serviceProvider.GetService<TelemetryClient>();
            var hostedServiceSetting = _serviceProvider.GetService<IOptions<AccountingHostedServiceSetting>>();
            var kafkaSettings = _serviceProvider.GetService<IOptions<KafkaSettings>>();
            var kafkaRaw = _serviceProvider.GetService<IPosterKafkaProducer>();
            var businessInfo = _serviceProvider.GetService<IBusinessInfoService>();

            
            _posterActor = Sys.ActorOf(Props.Create(() =>
                    new PosterActor(statsDPublisher,
                        _fixture.SalesDataService,
                        logger,
                        kafkaSettings,
                        hostedServiceSetting,
                        kafkaRaw, businessInfo)),
                nameof(PosterActor));
        }
        
        
        [Fact]
        public void Perform_Posting_Task()
        {

            //arrange
            var expectedPayments = new List<Payment>(); 
            
            _fixture.SalesDataService.GetPaymentsAsync(Arg.Any<DateTime>())
                .Returns(Task.FromResult((expectedPayments, true, DateTime.MinValue, DateTime.MinValue)));        
            //act
            _posterActor.Tell("start");

            ExpectNoMsg(TimeSpan.FromSeconds(10));


        }


    }
}
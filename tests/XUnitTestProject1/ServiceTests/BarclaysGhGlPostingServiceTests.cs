using System;
using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services;
using Hubtel.Accounting.Processor.Services.ProviderPostings;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk;
using Hubtel.Accounting.Sdk.Models;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Hubtel.Accounting.Processor.Tests.ServiceTests
{
    public class BarclaysGhGlPostingServiceTests
    {
        [Fact]
        public async Task PostEntry_SuccessfulPosting_LogsInformation()
        {
            // Arrange
            var loggerMock = Substitute.For<ILogger<BarclaysGhGlPostingService>>();
            var errorProducerMock = Substitute.For<IErrorProducer>();
            var accountingApiMock = Substitute.For<IAccountingApi>();
            accountingApiMock.PostGl(Arg.Any<GlRequest>())
                .Returns(new GlResponse { Status = "2000" });
            var service = new BarclaysGhGlPostingService(
                loggerMock,
                errorProducerMock,
                accountingApiMock);
            var payment = new Payment
            {
                AmountAfterCharges = 100,
                Fee = 10,
                AmountPaid = 110,
                PaymentDate = DateTime.UtcNow,
                FineractSavingsAccountId = 12345
            };

            // Act
            await service.PostEntry(payment);

            // Assert
            await accountingApiMock.Received(1).PostGl(Arg.Any<GlRequest>());
            await errorProducerMock.DidNotReceive().Produce(Arg.Any<PaymentAggregateError>());
        }

        [Fact]
        public async Task PostEntry_FailedPosting_ProducesErrorAndLogsWarning()
        {
            // Arrange
            var loggerMock = Substitute.For<ILogger<BarclaysGhGlPostingService>>();
            var errorProducerMock = Substitute.For<IErrorProducer>();
            var accountingApiMock = Substitute.For<IAccountingApi>();
            accountingApiMock.PostGl(Arg.Any<GlRequest>())
                .Returns(new GlResponse { Status = "2001", RawResponse = "error" });
            var service = new BarclaysGhGlPostingService(
                loggerMock,
                errorProducerMock,
                accountingApiMock);
            var payment = new Payment
            {
                AmountAfterCharges = 100,
                Fee = 10,
                AmountPaid = 110,
                PaymentDate = DateTime.UtcNow,
                FineractSavingsAccountId = 12345
            };

            // Act
            await service.PostEntry(payment);

            // Assert
            await accountingApiMock.Received(1).PostGl(Arg.Any<GlRequest>());
            await errorProducerMock.Received(1).Produce(Arg.Any<PaymentAggregateError>());
          
        }
    }
}

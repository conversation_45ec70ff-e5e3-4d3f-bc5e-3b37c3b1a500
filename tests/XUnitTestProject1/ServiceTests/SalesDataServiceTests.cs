// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Threading.Tasks;
// using FluentAssertions;
// using FluentAssertions.Execution;
// using Hubtel.Accounting.Data;
// using Hubtel.Accounting.Data.EntityModels;
// using Hubtel.Accounting.Data.Repository;
// using Hubtel.Accounting.Processor.Config;
// using Hubtel.Accounting.Processor.Services;
// using Hubtel.Accounting.Processor.Services.Providers;
// using Microsoft.ApplicationInsights;
// using Microsoft.ApplicationInsights.Extensibility;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.DependencyInjection;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
// using NSubstitute;
// using Xunit;
//
// namespace Hubtel.Accounting.Processor.Tests.ServiceTests
// {
//     public class SalesDataServiceTests
//     {
//         private readonly ILogger<SalesDataService> _logger =
//             Substitute.For<ILogger<SalesDataService>>();
//
//         private readonly TelemetryClient _telemetryClient =
//             new TelemetryClient(new TelemetryConfiguration());
//
//         private readonly IOptions<DatabaseSettings> _config =
//             Options.Create(new DatabaseSettings());
//         
//         private readonly IPaymentQueryRepository _paymentQueryRepository =
//             Substitute.For<IPaymentQueryRepository>();
//         
//
//         private ServiceProvider _inMemoryProvider;
//
//         private DbContextOptions<AccountingDbContext> AccountingOptions()
//         {
//             if (_inMemoryProvider == null)
//             {
//                 _inMemoryProvider = new ServiceCollection()
//                     .AddEntityFrameworkInMemoryDatabase()
//                     .BuildServiceProvider();
//             }
//
//             return new DbContextOptionsBuilder<AccountingDbContext>()
//                 .UseInMemoryDatabase(Guid.NewGuid().ToString())
//                 .UseInternalServiceProvider(_inMemoryProvider)
//                 .Options;
//         }
//
//         private DbContextOptions<UnifiedSalesContext> UnifiedOptions(string dbName)
//         {
//             if (_inMemoryProvider == null)
//             {
//                 _inMemoryProvider = new ServiceCollection()
//                     .AddEntityFrameworkInMemoryDatabase()
//                     .BuildServiceProvider();
//             }
//
//             return new DbContextOptionsBuilder<UnifiedSalesContext>()
//                 .UseInMemoryDatabase(dbName)
//                 .UseInternalServiceProvider(_inMemoryProvider)
//                 .Options;
//         }
//
//         #region GetPaymentsV2Async
//
//            [Fact]
//            public async Task GetPaymentsV2Async_WithPayments_ReturnsGroupedPayments_TracksLogsAndTelemetry()
//            {
//                // Arrange
//                var now = DateTime.UtcNow;
//                var end = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 0, DateTimeKind.Utc);
//                var start = end.AddHours(-1);
//                var payments = new List<NewPayment>
//                {
//                    new NewPayment
//                    {
//                        Id = "1",
//                        FineractSavingsAccountId = 11684,
//                        AmountPaid = 100m,
//                        Charges = 5m,
//                        Tips = 2m,
//                        AmountPlusTips = 102m,
//                        TipSettlement = 2m,
//                        AmountAfterCharges = 95m,
//                        DeliveryFee = 1m,
//                        ElevyAmount = 0m,
//                        IsSuccessful = true,
//                        PaymentProcessor = "procA",
//                        MomoChannel = null,
//                        CardTransactionMode = null,
//                        PaymentType = "card",
//                        BusinessId = "7",
//                        CreatedAt = end.AddMinutes(-30),
//                        PaymentDate = end.AddMinutes(-30)
//                    },
//                    // second payment in same group
//                    new NewPayment
//                    {
//                        Id = "2",
//                        FineractSavingsAccountId = 11684,
//                        AmountPaid = 50m,
//                        Charges = 3m,
//                        Tips = 1m,
//                        AmountPlusTips = 51m,
//                        TipSettlement = 1m,
//                        AmountAfterCharges = 49m,
//                        DeliveryFee = 1m,
//                        ElevyAmount = 0m,
//                        IsSuccessful = true,
//                        PaymentProcessor = "procA",
//                        MomoChannel = null,
//                        CardTransactionMode = null,
//                        PaymentType = "card",
//                        BusinessId = "7",
//                        CreatedAt = end.AddMinutes(-20),
//                        PaymentDate = end.AddMinutes(-20)
//                    }
//                };
//
//                _paymentQueryRepository.GetPaymentsAsync(start, end).Returns(Task.FromResult(payments));
//
//                var accountingContext = new AccountingDbContext(AccountingOptions());
//                using var unifiedContext = new UnifiedSalesContext(UnifiedOptions(Guid.NewGuid().ToString()));
//                var service = new SalesDataService(_telemetryClient,
//                    _logger,
//                    accountingContext,
//                    unifiedContext,
//                    _paymentQueryRepository);
//
//                // Act
//                var (resultPayments, success, actualStart, actualEnd) =
//                    await service.GetPaymentsV2Async(end.AddMinutes(5));
//
//                // Assert
//                using (new AssertionScope())
//                {
//                    success.Should().BeTrue();
//                    resultPayments.Should().HaveCount(1);
//                    resultPayments.All(p => p.FineractSavingsAccountId == 11684).Should().BeTrue();
//                    actualStart.Should().Be(start);
//                    actualEnd.Should().Be(end);
//                }
//            }
//            
//
//         [Fact]
//         public async Task GetPaymentsV2Async_NoPayments_LogsWarningAndReturnsFalse()
//         {
//             // Arrange: empty UnifiedSalesContext
//             using var unifiedContext = new UnifiedSalesContext(UnifiedOptions(Guid.NewGuid().ToString()));
//             using var accountingContext = new AccountingDbContext(AccountingOptions());
//             var service = new SalesDataService(_telemetryClient,
//                 _logger,
//                 accountingContext,
//                 unifiedContext,
//                 _paymentQueryRepository);
//
//             // Act
//             var (payments, succeeded, _, _) = await service.GetPaymentsV2Async(DateTime.UtcNow);
//
//             // Assert
//             succeeded.Should().BeFalse();
//             payments.Should().BeEmpty();
//         }
//
//         [Fact]
//         public async Task GetPaymentsV2Async_DbThrows_TracksExceptionAndReturnsFalse()
//         {
//             // Arrange: substitute UnifiedSalesContext to throw
//             var badUnified = Substitute.For<UnifiedSalesContext>(UnifiedOptions(Guid.NewGuid().ToString()));
//
//             using var accountingContext = new AccountingDbContext(AccountingOptions());
//             var service = new SalesDataService(_telemetryClient,
//                 _logger,
//                 accountingContext,
//                 badUnified,
//                 _paymentQueryRepository);
//
//             // Act
//             var (payments, succeeded, _, _) = await service.GetPaymentsV2Async(DateTime.UtcNow);
//
//             // Assert
//             succeeded.Should().BeFalse();
//             payments.Should().BeEmpty();
//         }
//         
//         #endregion
//
//
//         #region GetPaymentsAsync Tests
//
//         [Fact]
//         public async Task GetPaymentsAsync_NoPayments_LogsWarningAndReturnsFalse()
//         {
//             // Arrange: use in-memory UnifiedSalesContext with no data
//             using var unifiedContext = new UnifiedSalesContext(UnifiedOptions(Guid.NewGuid().ToString()));
//             using var accountingContext = new AccountingDbContext(AccountingOptions());
//
//             var service = new SalesDataService(_telemetryClient,
//                 _logger,
//                 accountingContext,
//                 unifiedContext,
//                 _paymentQueryRepository);
//
//             // Act
//             var (payments, succeeded, _, _) = await service.GetPaymentsAsync(DateTime.UtcNow);
//
//             // Assert
//             succeeded.Should().BeFalse();
//             payments.Should().BeEmpty();
//         }
//
//         [Fact]
//         public async Task GetPaymentsAsync_DbThrows_TracksExceptionAndReturnsFalse()
//         {
//             // Arrange: substitute UnifiedSalesContext that throws when accessing Payments
//             var badUnified = Substitute.For<UnifiedSalesContext>(UnifiedOptions(Guid.NewGuid().ToString()));
//
//             using var accountingContext = new AccountingDbContext(AccountingOptions());
//
//             var service = new SalesDataService(_telemetryClient,
//                 _logger,
//                 accountingContext,
//                 badUnified,
//                 _paymentQueryRepository);
//
//             // Act
//             var (payments, succeeded, _, _) = await service.GetPaymentsAsync(DateTime.UtcNow);
//
//             // Assert
//             succeeded.Should().BeFalse();
//             payments.Should().BeEmpty();
//         }
//
//         #endregion
//         
//         #region GroupPayments Tests
//
//         public static TheoryData<List<NewPayment>, int, decimal, decimal> GroupPaymentsTestData => 
//             new()
//             {
//                 {
//                     [
//                         new NewPayment
//                         {
//                             Id = "1",
//                             FineractSavingsAccountId = 11684,
//                             AmountPaid = 500,
//                             Charges = 10,
//                             Tips = 5,
//                             AmountPlusTips = 505,
//                             TipSettlement = 5,
//                             AmountAfterCharges = 490,
//                             DeliveryFee = 0,
//                             ElevyAmount = 0,
//                             PaymentProcessor = "MTNGHReceiveMoney - Default",
//                             MomoChannel = null,
//                             CardTransactionMode = null,
//                             PaymentType = "MobileMoney",
//                             BusinessId = "",
//                             CreatedAt = DateTime.UtcNow,
//                             PaymentDate = DateTime.UtcNow
//                         }
//                     ],
//                     1,    // expected groups
//                     500m, // total amount
//                     10m   // total fees
//                 }
//             };
//
//         [Theory]
//         [System.Diagnostics.CodeAnalysis.SuppressMessage("Usage", "xUnit1045:The type argument might not be serializable", Justification = "Test data works correctly despite serialization warning")]
//         [MemberData(nameof(GroupPaymentsTestData))]
//         public void GroupPayments_ReturnsExpectedGroups(List<NewPayment> payments,
//             int expectedGroupCount, decimal expectedTotalAmount, decimal expectedTotalFees)
//         {
//             var service = new SalesDataService(_telemetryClient, _logger, 
//                 new AccountingDbContext(AccountingOptions()),
//                 new UnifiedSalesContext(UnifiedOptions(Guid.NewGuid().ToString())),
//                     _paymentQueryRepository);
//
//             var groups = service.GroupPayments(payments).ToList();
//
//             groups.Count.Should().Be(expectedGroupCount);
//             var group = groups.Single();
//             group.TotalAmount.Should().Be(expectedTotalAmount);
//             group.TotalFees.Should().Be(expectedTotalFees);
//         }
//
//         #endregion
//
//         #region npgsql
//         [Fact]
//         public async Task UpdatePaymentSSyncedStatusAsync_WhenContextIsNull_ReturnsMinusOne()
//         {
//             // Arrange
//             using var accountingContext = new AccountingDbContext(AccountingOptions());
//             
//             // Pass null for unifiedSalesContext to force an NRE
//             var service = new SalesDataService(_telemetryClient,
//                 _logger,
//                 accountingContext,
//                 unifiedSalesContext: null!,
//                 paymentQueryRepository: _paymentQueryRepository);
//
//             // Act
//             var result = await service.UpdatePaymentSSyncedStatusAsync(
//                 ids: "1,2,3",
//                 batchId: "batch-1",
//                 start: DateTime.UtcNow.AddHours(-1),
//                 end: DateTime.UtcNow,
//                 status: true);
//
//             // Assert
//             result.Should().Be(-1);
//         }
//         
//         #endregion
//
//         #region LastQueryPoint
//         [Fact]
//         public async Task GetLastQueryPointAsync_WhenNoLastQuery_ReturnsNewLastPaymentQuery()
//         {
//             // Arrange
//             using var accountingContext = new AccountingDbContext(AccountingOptions());
//             var service = new SalesDataService(_telemetryClient,
//                 _logger,
//                 accountingContext,
//                 new UnifiedSalesContext(UnifiedOptions(Guid.NewGuid().ToString())),
//                 _paymentQueryRepository);
//
//             // Act
//             var result = await service.GetLastQueryPointAsync();
//
//             // Assert
//             result.Should().NotBeNull();
//             result.Id.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
//             result.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
//           
//         }
//         
//         [Fact]
//         public async Task GetLastQueryPointAsync_WhenExceptionThrown_ReturnsNull()
//         {
//             // Arrange
//             var service = new SalesDataService(_telemetryClient,
//                 _logger,
//                 null,
//                 new UnifiedSalesContext(UnifiedOptions(Guid.NewGuid().ToString())),
//                 _paymentQueryRepository);
//
//             // Act
//             var result = await service.GetLastQueryPointAsync();
//
//             // Assert
//             result.Should().BeNull();
//         }
//
//         #endregion
//         
//         
//         [Fact]
//         public async Task StoreLastQueryPointAsync_StoresQueryPoints_ReturnsTrue()
//         {
//             var service = new SalesDataService(_telemetryClient,
//                 _logger,
//                 new AccountingDbContext(AccountingOptions()),
//                 new UnifiedSalesContext(UnifiedOptions(Guid.NewGuid().ToString())),
//                 _paymentQueryRepository
//             );
//
//             var lastQuery = new LastPaymentQuery
//             {
//                 Id = DateTime.UtcNow,
//                 CreatedAt = DateTime.UtcNow
//             };
//
//             // Act
//             var result = await service.StoreLastQueryPointAsync(lastQuery);
//
//             // Assert
//             result.Should().BeTrue();
//            
//         }
//     }
// }
// using System;
// using System.Threading.Tasks;
// using FluentAssertions;
// using Hubtel.Accounting.Processor.Config;
// using Hubtel.Accounting.Processor.Services.Providers;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
// using NSubstitute;
// using StackExchange.Redis;
// using Xunit;
// using Flurl.Http.Testing;
// using Hubtel.Redis.Sdk.Services;
// using NSubstitute.ExceptionExtensions;
//
//
// namespace Hubtel.Accounting.Processor.Tests.ServiceTests;
//
// public class BusinessInfoServiceTests
// {
//     private readonly ILogger<BusinessInfoService> _logger;
//     private readonly IDatabase _redisDb;
//     private readonly IOptions<BusinessInfoConfig> _businessInfoConfig;
//     private readonly IOptions<RedisExtra> _redisExtra;
//     private readonly IMultiRedisHostCacheRepository _redisCacheRepo;
//     private readonly BusinessInfoService _businessInfoService;
//     private readonly HttpTest _httpTest;
//
//     public BusinessInfoServiceTests()
//     {
//         var fixture = new DiFixture();
//         
//         _logger = Substitute.For<ILogger<BusinessInfoService>>();
//         _redisDb = fixture.RedisDb;
//         _redisCacheRepo = fixture.MultiHostRedis;
//         _businessInfoConfig = Options.Create(new BusinessInfoConfig
//         {
//             TransactionBaseUrl = "https://api.test.com",
//             Auth = "test-auth-key"
//         });
//         _redisExtra = Options.Create(new RedisExtra
//         {
//             Key = "test:bizinfo:",
//             CacheTransactionTimeout = 600
//         });
//
//         // Mock the GetDb method to return our mock database
//
//         _businessInfoService = new BusinessInfoService(
//             _logger,
//             _redisExtra,
//             _redisCacheRepo,
//             _businessInfoConfig);
//
//         _httpTest = new HttpTest();
//     }
//     
//     [Fact]
//     public async Task IsBusinessOnBulkSettlement_WithCachedData_ReturnsCachedResult()
//     {
//         // Arrange
//         var businessId = "test-business-123";
//         var cachedResponse = "{\"isBulkSettlement\": true}";
//         _redisDb.StringGetAsync($"test:bizinfo:{businessId}")
//             .Returns(cachedResponse);
//
//         // Act
//         var result = await _businessInfoService.IsBusinessOnBulkSettlement(businessId);
//
//         // Assert
//         result.Should().BeTrue();
//         await _redisDb.Received(1).StringGetAsync($"test:bizinfo:{businessId}");
//     }
//
//     [Fact]
//     public async Task IsBusinessOnBulkSettlement_WithoutCache_MakesApiCall()
//     {
//         // Arrange
//         var businessId = "test-business-123";
//         _redisDb.StringGetAsync(Arg.Any<RedisKey>()).Returns((RedisValue)RedisValue.Null);
//
//         _httpTest.RespondWith("{\"isBulkSettlement\": false}", 200);
//
//         // Act
//         var result = await _businessInfoService.IsBusinessOnBulkSettlement(businessId);
//
//         // Assert
//         _httpTest.ShouldHaveCalled($"https://api.test.com/{businessId}/no-keys")
//             .WithHeader("Authorization", "PrivateKey test-auth-key");
//     }
//
//     [Fact]
//     public async Task IsBusinessOnBulkSettlement_ApiCallFails_ReturnsFalse()
//     {
//         // Arrange
//         var businessId = "test-business-123";
//         _redisDb.StringGetAsync(Arg.Any<RedisKey>()).Returns((RedisValue)RedisValue.Null);
//
//         _httpTest.RespondWith("Server Error", 500);
//
//         // Act
//         var result = await _businessInfoService.IsBusinessOnBulkSettlement(businessId);
//
//         // Assert
//         result.Should().BeFalse();
//     }
//
//     [Fact]
//     public async Task IsBusinessOnBulkSettlement_ExceptionThrown_LogsErrorAndReturnsFalse()
//     {
//         // Arrange
//         var businessId = "test-business-123";
//         var exception = new Exception("Redis connection failed");
//         _redisDb.StringGetAsync(Arg.Any<RedisKey>()).Throws(exception);
//
//         // Act
//         var result = await _businessInfoService.IsBusinessOnBulkSettlement(businessId);
//
//         // Assert
//         result.Should().BeFalse();
//     }
//     
// }
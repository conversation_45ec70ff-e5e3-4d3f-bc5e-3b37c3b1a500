using System.Threading.Tasks;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.ProviderPostings;
using Hubtel.Accounting.Processor.Services.Providers;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Xunit;

namespace Hubtel.Accounting.Processor.Tests.ServiceTests
{
    public class GlPostingServiceTests
    {
        private readonly ILogger<GlPostingService> _logger;
        private readonly IMtnGhGlPostingService _mtnService;
        private readonly IVodafoneGhGlPostingService _vodafoneService;
        private readonly ITigoGhGlPostingService _tigoService;
        private readonly IAirtelGhGlPostingService _airtelService;
        private readonly IPayworksZenithGhGlPostingService _payworksService;
        private readonly IEcobankGhGlPostingService _ecobankService;
        private readonly IBarclaysGhGlPostingService _barclaysService;
        private readonly IHubtelGlPostingService _hubtelService;
        private readonly IGhqrGlPostingService _ghqrService;
        private readonly GlPostingService _glPostingService;
        private readonly GlPostingServiceDependencies _dependencies;

        public GlPostingServiceTests()
        {
            _logger = Substitute.For<ILogger<GlPostingService>>();
            _mtnService = Substitute.For<IMtnGhGlPostingService>();
            _vodafoneService = Substitute.For<IVodafoneGhGlPostingService>();
            _tigoService = Substitute.For<ITigoGhGlPostingService>();
            _airtelService = Substitute.For<IAirtelGhGlPostingService>();
            _payworksService = Substitute.For<IPayworksZenithGhGlPostingService>();
            _ecobankService = Substitute.For<IEcobankGhGlPostingService>();
            _barclaysService = Substitute.For<IBarclaysGhGlPostingService>();
            _hubtelService = Substitute.For<IHubtelGlPostingService>();
            _ghqrService = Substitute.For<IGhqrGlPostingService>();
            
            _dependencies = new GlPostingServiceDependencies
            {
                MtnGhGlPostingService = _mtnService,
                VodafoneGhGlPostingService = _vodafoneService,
                TigoGhGlPostingService = _tigoService,
                AirtelGhGlPostingService = _airtelService
            };
            
            _glPostingService = new GlPostingService(_logger,
                _dependencies,
                _payworksService,
                _ecobankService,
                _barclaysService,
                _hubtelService,
                _ghqrService);
        }

        [Fact]
        public async Task PostGlEntries_ShouldCallMtnService_ForMobileMoneyMtn()
        {
            var payment = new Payment
            {
                PaymentType = "MobileMoney",
                MobileMoney = new MobileMoneyData { Network = "mtn-gh" }
            };

            await _glPostingService.PostGlEntries(payment);

            await _mtnService.Received(1).PostEntry(payment);
        }

        [Fact]
        public async Task PostGlEntries_ShouldCallVodafoneService_ForMobileMoneyVodafone()
        {
            var payment = new Payment
            {
                PaymentType = "MobileMoney",
                MobileMoney = new MobileMoneyData { Network = "vodafone-gh" }
            };

            await _glPostingService.PostGlEntries(payment);

            await _vodafoneService.Received(1).PostEntry(payment);
        }

        [Fact]
        public async Task PostGlEntries_ShouldCallTigoService_ForMobileMoneyTigo()
        {
            var payment = new Payment
            {
                PaymentType = "MobileMoney",
                MobileMoney = new MobileMoneyData { Network = "tigo-gh" }
            };

            await _glPostingService.PostGlEntries(payment);

            await _tigoService.Received(1).PostEntry(payment);
        }

        [Fact]
        public async Task PostGlEntries_ShouldCallAirtelService_ForMobileMoneyAirtel()
        {
            var payment = new Payment
            {
                PaymentType = "MobileMoney",
                MobileMoney = new MobileMoneyData { Network = "airtel-gh" }
            };

            await _glPostingService.PostGlEntries(payment);

            await _airtelService.Received(1).PostEntry(payment);
        }

        [Fact]
        public async Task PostGlEntries_ShouldCallPayworksService_ForCardPresent()
        {
            var payment = new Payment
            {
                PaymentType = "Card",
                Card = new CardData { TransactionMode = "card_present" }
            };

            await _glPostingService.PostGlEntries(payment);

            await _payworksService.Received(1).PostEntry(payment);
        }

        [Fact]
        public async Task PostGlEntries_ShouldCallEcobankService_ForCardNotPresentEcobank()
        {
            var payment = new Payment
            {
                PaymentType = "Card",
                Card = new CardData { TransactionMode = "card_not_present", Processor = "ecobank-gh" }
            };

            await _glPostingService.PostGlEntries(payment);

            await _ecobankService.Received(1).PostEntry(payment);
        }

        [Fact]
        public async Task PostGlEntries_ShouldCallBarclaysService_ForCardNotPresentBarclays()
        {
            var payment = new Payment
            {
                PaymentType = "Card",
                Card = new CardData { TransactionMode = "card_not_present", Processor = "barclays-gh" }
            };

            await _glPostingService.PostGlEntries(payment);

            await _barclaysService.Received(1).PostEntry(payment);
        }

        [Fact]
        public async Task PostGlEntries_ShouldCallHubtelService_ForHubtelPaymentType()
        {
            var payment = new Payment
            {
                PaymentType = "Hubtel"
            };

            await _glPostingService.PostGlEntries(payment);

            await _hubtelService.Received(1).PostEntry(payment);
        }

        [Fact]
        public async Task PostGlEntries_ShouldCallGhqrService_ForGhqrPaymentType()
        {
            var payment = new Payment
            {
                PaymentType = "Ghqr"
            };

            await _glPostingService.PostGlEntries(payment);

            await _ghqrService.Received(1).PostEntry(payment);
        }
    }
}
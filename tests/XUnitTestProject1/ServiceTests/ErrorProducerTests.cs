// using System;
// using System.Threading;
// using System.Threading.Tasks;
// using Confluent.Kafka;
// using FluentAssertions;
// using Hubtel.Accounting.Processor.Config;
// using Hubtel.Accounting.Processor.Models;
// using Hubtel.Accounting.Processor.Services.Providers;
// using Microsoft.Extensions.Options;
// using Newtonsoft.Json;
// using NSubstitute;
// using Xunit;
//
// namespace Hubtel.Accounting.Processor.Tests.ServiceTests;
//
// public class ErrorProducerTests
// {
//     private readonly ErrorProducer _errorProducer;
//     private readonly IOptions<KafkaSettings> _kafkaSettings;
//     private readonly IProducer<Null, string> _producerMock;
//
//     public ErrorProducerTests()
//     {
//         _kafkaSettings = Options.Create(new KafkaSettings
//         {
//             BootstrapServers = "localhost:9092",
//             ErrorTopic = "error-topic"
//         });
//
//         _producerMock = Substitute.For<IProducer<Null, string>>();
//         _errorProducer = new ErrorProducer(_kafkaSettings, _producerMock);
//     }
//
//     [Fact]
//     public async Task Produce_WithValidPaymentAggregateError_ShouldSerializeAndProduce()
//     {
//         // Arrange
//         var paymentError = new PaymentAggregateError
//         {
//             Data = new Payment { TransactionId = "TXN123", Fee = 10 },
//             Message = "Test error message",
//             Stage = "test_error"
//         };
//
//         var expectedMessage = JsonConvert.SerializeObject(paymentError);
//
//         // Act
//         await _errorProducer.Produce(paymentError);
//
//         // Assert
//         await _producerMock
//             .Received(1)
//             .ProduceAsync(
//                 _kafkaSettings.Value.ErrorTopic,
//                 Arg.Is<Message<Null, string>>(m => m.Value == expectedMessage),
//                 Arg.Any<CancellationToken>()
//             );
//     }
//
//     [Fact]
//     public void Constructor_WithValidKafkaSettings_ShouldNotThrow()
//     {
//         // Act
//         ErrorProducer producer = null;
//         Action act = () => producer = new ErrorProducer(_kafkaSettings, _producerMock);
//
//         // Assert
//         act.Should().NotThrow();
//         producer.Should().NotBeNull();
//     }
//
//     [Fact]
//     public async Task Produce_WithComplexPaymentData_ShouldSerializeCorrectly()
//     {
//         // Arrange
//         var payment = new Payment
//         {
//             TransactionId = "TXN456",
//             Fee = 25.50m,
//             AmountPaid = 100.00m,
//             PaymentType = "MobileMoney",
//             MobileMoney = new MobileMoneyData { Network = "MTN" },
//             FineractSavingsAccountId = 12345
//         };
//
//         var paymentError = new PaymentAggregateError
//         {
//             Data = payment,
//             Message = "Complex payment error",
//             Stage = "deposit_error"
//         };
//
//         var expectedMessage = JsonConvert.SerializeObject(paymentError);
//
//         // Act
//         await _errorProducer.Produce(paymentError);
//
//         // Assert
//         await _producerMock
//             .Received(1)
//             .ProduceAsync(
//                 _kafkaSettings.Value.ErrorTopic,
//                 Arg.Is<Message<Null, string>>(m => m.Value == expectedMessage),
//                 Arg.Any<CancellationToken>()
//             );
//     }
// }
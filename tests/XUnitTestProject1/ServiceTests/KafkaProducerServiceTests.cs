// using System;
// using System.Collections.Generic;
// using System.Threading.Tasks;
// using Confluent.Kafka;
// using FluentAssertions;
// using Hubtel.Accounting.Processor.Services.Providers;
// using Hubtel.Kafka.Producer.Sdk.Options;
// using Hubtel.Kafka.Producer.Sdk.Services;
// using Microsoft.Extensions.Logging;
// using Microsoft.Extensions.Options;
// using NSubstitute;
// using NSubstitute.ExceptionExtensions;
// using Xunit;
//
// namespace Hubtel.Accounting.Processor.Tests.ServiceTests
// {
//     public class KafkaProducerServiceTests
//     {
//         private readonly IKafkaProducer _kafkaProducer;
//         private readonly IKafkaProducerFactory _kafkaProducerFactory;
//         private readonly KafkaProducerService _kafkaProducerService;
//         private readonly ILogger<KafkaProducerService> _logger;
//
//         public KafkaProducerServiceTests()
//         {
//             _kafkaProducer = Substitute.For<IKafkaProducer>();
//             _kafkaProducerFactory = Substitute.For<IKafkaProducerFactory>();
//             _logger = Substitute.For<ILogger<KafkaProducerService>>();
//
//             var kafkaProducerConfig = Options.Create(new KafkaProducerConfig
//             {
//                 Hosts = new List<KafkaServerConfig>() { new() { Alias = "test-host" } }
//             });
//
//             _kafkaProducerFactory.CreateKafkaProducer("test-host").Returns(_kafkaProducer);
//
//             _kafkaProducerService = new KafkaProducerService(_kafkaProducerFactory, _logger, kafkaProducerConfig);
//         }
//
//         [Fact]
//         public void Constructor_WithValidConfig_SetsUpProducer()
//         {
//             // SUT was constructed in the test-ctor
//             _kafkaProducerService.Should().NotBeNull();
//             _kafkaProducerFactory.Received(1).CreateKafkaProducer("test-host");
//         }
//
//         [Fact]
//         public async Task ProduceAsync_WithNullTopic_ThrowsInvalidOperationException()
//         {
//             // Arrange
//             var payload = new { Name = "Test" };
//             var kafkaException = new ArgumentException("Topic cannot be null");
//
//             _kafkaProducer.ProduceAsync(null!, "{\"Name\":\"Test\"}").Throws(kafkaException);
//
//             // Act
//             Func<Task> act = async () => await _kafkaProducerService.ProduceAsync(null, payload);
//
//             // Assert
//             await act.Should().ThrowAsync<InvalidOperationException>()
//                 .WithMessage("Failed to produce message to Kafka topic ''");
//
//             // Verify logging occurred
//             _logger.Received(1).Log(
//                 LogLevel.Error,
//                 Arg.Any<EventId>(),
//                 Arg.Any<object>(),
//                 kafkaException,
//                 Arg.Any<Func<object, Exception, string>>());
//         }
//
//         [Fact]
//         public async Task ProduceAsync_WithEmptyTopic_ThrowsInvalidOperationException()
//         {
//             // Arrange
//             var topic = "";
//             var payload = new { Name = "Test" };
//             var kafkaException = new KafkaException(ErrorCode.TopicException);
//
//             _kafkaProducer.ProduceAsync(topic, "{\"Name\":\"Test\"}").Throws(kafkaException);
//
//             // Act
//             Func<Task> act = async () => await _kafkaProducerService.ProduceAsync(topic, payload);
//
//             // Assert
//             await act.Should().ThrowAsync<InvalidOperationException>()
//                 .WithMessage("Failed to produce message to Kafka topic ''");
//     
//             // Verify logging occurred
//             _logger.Received(1).Log(
//                 LogLevel.Error,
//                 Arg.Any<EventId>(),
//                 Arg.Any<object>(),
//                 kafkaException,
//                 Arg.Any<Func<object, Exception, string>>());
//         }
//
//         [Fact]
//         public async Task ProduceAsync_WithNullPayload_ThrowsInvalidOperationException()
//         {
//             // Arrange
//             var topic = "test-topic";
//             var serializationException = new ArgumentNullException("value");
//
//             _kafkaProducer.ProduceAsync(topic, "null").Throws(serializationException);
//
//             // Act
//             Func<Task> act = async () => await _kafkaProducerService.ProduceAsync(topic, null);
//
//             // Assert
//             await act.Should().ThrowAsync<InvalidOperationException>()
//                 .WithMessage("Failed to produce message to Kafka topic 'test-topic'");
//     
//             // Verify logging occurred
//             _logger.Received(1).Log(
//                 LogLevel.Error,
//                 Arg.Any<EventId>(),
//                 Arg.Any<object>(),
//                 serializationException,
//                 Arg.Any<Func<object, Exception, string>>());
//         }
//
//         [Fact]
//         public async Task ProduceAsync_WithValidTopicAndPayload_ProducesMessage()
//         {
//             // Arrange
//             var topic = "test-topic";
//             var payload = new { Name = "Test" };
//             var deliveryResult = new DeliveryResult<Null, string>
//             {
//                 Topic = topic,
//                 Partition = 0,
//                 Status = PersistenceStatus.Persisted,
//                 Message = new Message<Null, string> { Value = "{\"Name\":\"Test\"}" }
//             };
//
//             _kafkaProducer.ProduceAsync(topic, "{\"Name\":\"Test\"}").Returns(Task.FromResult(deliveryResult));
//
//             // Act
//             await _kafkaProducerService.ProduceAsync(topic, payload);
//
//             // Assert
//             await _kafkaProducer.Received(1).ProduceAsync(topic, "{\"Name\":\"Test\"}");
//         }
//
//         [Fact]
//         public async Task ProduceAsync_WithException_LogsErrorAndRethrows()
//         {
//             // Arrange
//             var topic = "test-topic";
//             var payload = new { Name = "Test" };
//             var exception = new Exception("Kafka connection failed");
//
//             _kafkaProducer.ProduceAsync(Arg.Any<string>(), Arg.Any<string>()).Throws(exception);
//
//             // Act
//             Func<Task> act = async () => await _kafkaProducerService.ProduceAsync(topic, payload);
//
//             // Assert
//             await act.Should().ThrowAsync<Exception>().WithMessage("Failed to produce message to Kafka topic 'test-topic'");
//         }
//     }
// }
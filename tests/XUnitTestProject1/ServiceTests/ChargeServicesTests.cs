using System;
using Xunit;
using NSubstitute;
using Hubtel.Accounting.Processor.Models;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Hubtel.Accounting.Processor.Config;
using System.Collections.Generic;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk;
using Hubtel.Accounting.Sdk.Constants;

namespace Hubtel.Accounting.Processor.Tests.ServiceTests
{
    public class ChargeServicesTests
    {
        private readonly ChargeService _chargeService;
        private readonly IErrorProducer _errorProducer;
        private readonly IAccountingApi _accountingApi;
        private readonly ILogger<ChargeService> _logger;
        private readonly IOptions<AccountingSettings> _accountingSettings;

        public ChargeServicesTests()
        {
            _errorProducer = Substitute.For<IErrorProducer>();
            _accountingApi = Substitute.For<IAccountingApi>();
            _logger = Substitute.For<ILogger<ChargeService>>();
            _accountingSettings = Options.Create(new AccountingSettings
            {
                ChargeIds = new List<AccountingCharge> {
                    new AccountingCharge {
                        Name = "TestType",
                        Channels = new List<AccountingChargeChannel> {
                            new AccountingChargeChannel { Name = "TestChannel", Id = 123 }
                        }
                    }
                }
            });
            _chargeService = new ChargeService(_logger, _errorProducer, _accountingApi, _accountingSettings);
        }

        [Fact]
        public async Task CreateCharge_ShouldReturn_WhenFeeIsZero()
        {
            var payment = new Payment { Fee = 0, PaymentType = "TestType", MobileMoney = new MobileMoneyData() };
            
            await _chargeService.CreateCharge(payment);
            await _errorProducer.DidNotReceive().Produce(Arg.Any<PaymentAggregateError>());
        }

        [Fact]
        public async Task CreateCharge_ShouldReturn_WhenPaymentTypeIsUnrecognized()
        {
            var payment = new Payment { Fee = 10, PaymentType = "UnknownType", MobileMoney = new MobileMoneyData() };
            
            await _chargeService.CreateCharge(payment);
            await _errorProducer.Received().Produce(Arg.Any<PaymentAggregateError>());
        }

        [Fact]
        public async Task CreateCharge_ShouldReturn_WhenChannelIsUnrecognized()
        {
            var payment = new Payment { Fee = 10, PaymentType = "TestType", MobileMoney = new MobileMoneyData { Network = "UnknownChannel" } };
          
            await _chargeService.CreateCharge(payment);
            await _errorProducer.Received().Produce(Arg.Any<PaymentAggregateError>());
        }

        [Fact]
        public async Task CreateCharge_ShouldCallAccountingApi_WhenValid()
        {
            var payment = new Payment { Fee = 10, PaymentType = "TestType", MobileMoney = new MobileMoneyData { Network = "TestChannel" }, FineractSavingsAccountId = 1 };
           
            _accountingApi.AddCharge(Arg.Any<AddChargeRequest>()).Returns(Task.FromResult(new Accounting.Sdk.Models.AddChargeResponse() {Status = "2000", Data = new Accounting.Sdk.Models. AccountingApiResponseData { ResourceId = 1 } }));
            _accountingApi.PayCharge(Arg.Any<PayChargeRequest>()).Returns(Task.FromResult(new Accounting.Sdk.Models.PayChargeResponse() { Status = "2000"}));
           
            await _chargeService.CreateCharge(payment);
            
            await _accountingApi.Received().AddCharge(Arg.Any<AddChargeRequest>());
            await _accountingApi.Received().PayCharge(Arg.Any<PayChargeRequest>());
        }
        
        [Fact]
        public async Task CreateCharge_ShouldProduceError_WhenAddChargeIsUnsuccessful()
        {
            // Arrange
            var payment = new Payment 
            { 
                Fee = 10, 
                PaymentType = "TestType", 
                MobileMoney = new MobileMoneyData { Network = "TestChannel" }, 
                FineractSavingsAccountId = 1,
                TransactionId = "TXN123"
            };
    
            _accountingApi.AddCharge(Arg.Any<AddChargeRequest>())
                .Returns(Task.FromResult(new Accounting.Sdk.Models.AddChargeResponse() 
                { 
                    Status = "4000", 
                    RawResponse = "Error occurred"
                }));

            // Act
            await _chargeService.CreateCharge(payment);
    
            // Assert
            await _errorProducer.Received().Produce(Arg.Is<PaymentAggregateError>(error => 
                error.Data == payment &&
                error.Message.Contains("could not post charge for transaction TXN123") &&
                error.Stage == PaymentErrors.Create_Charge_type_error));
    
            await _accountingApi.DidNotReceive().PayCharge(Arg.Any<PayChargeRequest>());
        }

        #region PayRmCharge

        [Fact]
        public async Task PayRmCharge_ShouldProduceError_WhenPayChargeIsUnsuccessful()
        {
            // Arrange
            var payment = new Payment 
            { 
                Fee = 10, 
                PaymentType = "TestType", 
                MobileMoney = new MobileMoneyData { Network = "TestChannel" }, 
                FineractSavingsAccountId = 1,
                TransactionId = "TXN456",
                PaymentDate = DateTime.UtcNow
            };
    
            var resourceId = 123L;
    
            _accountingApi.PayCharge(Arg.Any<PayChargeRequest>())
                .Returns(Task.FromResult(new Accounting.Sdk.Models.PayChargeResponse() 
                { 
                    Status = "4000", 
                    RawResponse = "Payment failed"
                }));

            // Act
            
            await _chargeService.PayRmCharge(payment, resourceId);
    
            // Assert
            await _errorProducer.Received().Produce(Arg.Is<PaymentAggregateError>(error => 
                error.Data == payment &&
                error.Message.Contains("could not pay charge for transaction TXN456") &&
                error.Message.Contains("Payment failed") &&
                error.Stage == PaymentErrors.Pay_Charge_type_error));
    
            await _accountingApi.Received().PayCharge(Arg.Is<PayChargeRequest>(req =>
                req.Amount == payment.Fee &&
                req.PayChargeId == resourceId &&
                req.AccountingType == AccountingType.ReceiveMoney));
        }

        #endregion

        #region PayCharge

        [Fact]
        public async Task PayCharge_ShouldCallAccountingApi_WhenValid()
        {
            // Arrange
            var payment = new Payment
            {
                Fee = 15.50m,
                PaymentDate = DateTime.UtcNow,
                FineractSavingsAccountId = 12345,
                TransactionId = "TXN789"
            };

            var resourceId = 456L;

            _accountingApi.PayCharge(Arg.Any<PayChargeRequest>())
                .Returns(Task.FromResult(new Accounting.Sdk.Models.PayChargeResponse()
                {
                    Status = "2000",
                }));

            // Act
            await _chargeService.PayCharge(payment, resourceId);

            // Assert
            await _accountingApi.Received().PayCharge(Arg.Is<PayChargeRequest>(req =>
                req.Amount == payment.Fee &&
                req.PayChargeId == resourceId &&
                req.DueTimeStamp == payment.PaymentDate));

            await _errorProducer.DidNotReceive().Produce(Arg.Any<PaymentAggregateError>());
        }

        [Fact]
        public async Task PayCharge_ShouldProduceError_WhenPayChargeIsUnsuccessful()
        {
            // Arrange
            var payment = new Payment
            {
                Fee = 20.00m,
                PaymentDate = DateTime.UtcNow,
                FineractSavingsAccountId = 67890,
                TransactionId = "TXN999"
            };

            var resourceId = 789L;

            _accountingApi.PayCharge(Arg.Any<PayChargeRequest>())
                .Returns(Task.FromResult(new Accounting.Sdk.Models.PayChargeResponse()
                {
                    Status = "4000",
                    RawResponse = "Payment processing failed"
                }));

            // Act
            await _chargeService.PayCharge(payment, resourceId);

            // Assert
            await _errorProducer.Received().Produce(Arg.Is<PaymentAggregateError>(error =>
                error.Data == payment &&
                error.Message.Contains("could not pay charge for transaction TXN999") &&
                error.Message.Contains("Payment processing failed") &&
                error.Stage == PaymentErrors.Pay_Charge_type_error));

            await _accountingApi.Received().PayCharge(Arg.Is<PayChargeRequest>(req =>
                req.Amount == payment.Fee &&
                req.PayChargeId == resourceId));
        }

        #endregion
    }
}


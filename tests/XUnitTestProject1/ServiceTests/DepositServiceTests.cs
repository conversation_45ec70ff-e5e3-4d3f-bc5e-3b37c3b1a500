using System;
using Xunit;
using NSubstitute;
using Hubtel.Accounting.Processor.Services;
using Hubtel.Accounting.Processor.Models;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Hubtel.Accounting.Processor.Config;
using Hubtel.Accounting.Processor.Constants;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk;
using NSubstitute.ExceptionExtensions;

namespace Hubtel.Accounting.Processor.Tests.ServiceTests
{
    public class DepositServiceTests
    {
        private readonly IDepositService _depositService;
        private readonly IChargeService _chargeService;
        private readonly IGlPostingService _glPostingService;
        private readonly IErrorProducer _errorProducer;
        private readonly IAccountingApi _accountingApi;
        private readonly IOptions<AccountingSettings> _accountingSettings;
        private readonly IOptions<BusinessInfoConfig> _businessInfoConfig;
        private readonly ILogger<DepositService> _logger;

        public DepositServiceTests()
        {
            _chargeService = Substitute.For<IChargeService>();
            _glPostingService = Substitute.For<IGlPostingService>();
            _errorProducer = Substitute.For<IErrorProducer>();
            _accountingApi = Substitute.For<IAccountingApi>();
            _accountingSettings = Options.Create(new AccountingSettings());
            _businessInfoConfig = Options.Create(new BusinessInfoConfig());
            _logger = Substitute.For<ILogger<DepositService>>();
            _depositService = new DepositService(_logger, _accountingSettings, _chargeService, _glPostingService,
                _accountingApi, _errorProducer, _businessInfoConfig);
        }

        [Fact]
        public async Task MakeDeposit_ShouldReturnFalse_WhenAmountPaidMinusFeeIsNegative()
        {
            var payment = new Payment { AmountPaid = 10, Fee = 20, FineractSavingsAccountId = 1 };
            var result = await _depositService.MakeDeposit(payment);
            Assert.False(result);
        }

        [Fact]
        public async Task MakeDeposit_ShouldReturnFalse_WhenFineractSavingsAccountIdIsZero()
        {
            var payment = new Payment { AmountPaid = 100, Fee = 10, FineractSavingsAccountId = 0 };
            var result = await _depositService.MakeDeposit(payment);
            Assert.False(result);
        }

        [Fact]
        public async Task MakeDeposit_ShouldReturnFalse_WhenPaymentTypeIsUnrecognized()
        {
            var payment = new Payment
                { AmountPaid = 100, Fee = 10, FineractSavingsAccountId = 1, PaymentType = "UnknownType" };
            var result = await _depositService.MakeDeposit(payment);
            Assert.False(result);
        }

        [Fact]
        public async Task MakeDeposit_ShouldReturnTrue_WhenDepositIsSuccessful()
        {
            var payment = new Payment
                { AmountPaid = 100, Fee = 10, FineractSavingsAccountId = 1, PaymentType = "TestType" };
            var settings = new AccountingSettings
            {
                PaymentTypes = new System.Collections.Generic.List<AccountingPaymentType>
                {
                    new AccountingPaymentType { Name = "TestType", Id = 1 }
                },
                ExcludedBusinesses = new System.Collections.Generic.List<string>()
            };

            var businessInfo = new BusinessInfoConfig();
            var accountingSettings = Options.Create(settings);
            var businessInfoConfig = Options.Create(businessInfo);
            var chargeService = Substitute.For<IChargeService>();
            var glPostingService = Substitute.For<IGlPostingService>();
            var errorProducer = Substitute.For<IErrorProducer>();
            var accountingApi = Substitute.For<IAccountingApi>();
            accountingApi.Deposit(Arg.Any<Accounting.Sdk.Models.DepositRequest>())
                .Returns(Task.FromResult(new Accounting.Sdk.Models.DepositResponse() { Status = "2000" }));
            var logger = Substitute.For<ILogger<DepositService>>();
            var depositService = new DepositService(logger, accountingSettings, chargeService, glPostingService,
                accountingApi, errorProducer, businessInfoConfig);

            var result = await depositService.MakeDeposit(payment);

            Assert.True(result);
        }
        
        [Fact]
        public async Task MakeDeposit_ShouldReturnTrue_WhenExcludedBusinessAndPaymentTypeIsPaysmallsmall()
        {
            var payment = new Payment
            {
                AmountPaid = 100, Fee = 10, FineractSavingsAccountId = 1, PaymentType = "paysmallsmall",
                BusinessId = "excludedBusiness"
            };
            var settings = new AccountingSettings
            {
                PaymentTypes = new System.Collections.Generic.List<AccountingPaymentType>
                {
                    new AccountingPaymentType { Name = "paysmallsmall", Id = 1 }
                },
                ExcludedBusinesses = new System.Collections.Generic.List<string> { "excludedBusiness" }
            };

            var businessInfo = new BusinessInfoConfig();
            var accountingSettings = Options.Create(settings);
            var businessInfoConfig = Options.Create(businessInfo);
            var chargeService = Substitute.For<IChargeService>();
            var glPostingService = Substitute.For<IGlPostingService>();
            var errorProducer = Substitute.For<IErrorProducer>();
            var accountingApi = Substitute.For<IAccountingApi>();
            accountingApi.Deposit(Arg.Any<Accounting.Sdk.Models.DepositRequest>())
                .Returns(Task.FromResult(new Accounting.Sdk.Models.DepositResponse() { Status = "2000" }));
            var logger = Substitute.For<ILogger<DepositService>>();

            var depositService = new DepositService(logger, accountingSettings, chargeService, glPostingService,
                accountingApi, errorProducer, businessInfoConfig);

            var result = await depositService.MakeDeposit(payment);

            Assert.True(result);
        }
        
        [Fact]
        public async Task MakeDeposit_ShouldProduceError_WhenDepositFails()
        {
            var payment = new Payment
            {
                AmountPaid = 100, Fee = 10, FineractSavingsAccountId = 1, PaymentType = "TestType"
            };
            var settings = new AccountingSettings
            {
                PaymentTypes = new System.Collections.Generic.List<AccountingPaymentType>
                {
                    new AccountingPaymentType { Name = "TestType", Id = 1 }
                },
                ExcludedBusinesses = new System.Collections.Generic.List<string>()
            };

            var businessInfo = new BusinessInfoConfig();
            var accountingSettings = Options.Create(settings);
            var businessInfoConfig = Options.Create(businessInfo);
            var chargeService = Substitute.For<IChargeService>();
            var glPostingService = Substitute.For<IGlPostingService>();
            var errorProducer = Substitute.For<IErrorProducer>();
            var accountingApi = Substitute.For<IAccountingApi>();
            
            accountingApi.Deposit(Arg.Any<Accounting.Sdk.Models.DepositRequest>())
                .Returns(Task.FromResult(new Accounting.Sdk.Models.DepositResponse() { Status = "5000" }));
            var logger = Substitute.For<ILogger<DepositService>>();
            var depositService = new DepositService(logger, accountingSettings, chargeService, glPostingService,
                accountingApi, errorProducer, businessInfoConfig);

            await depositService.MakeDeposit(payment);

            await errorProducer.Received(1).Produce(Arg.Is<PaymentAggregateError>(e =>
                e.Data == payment && e.Stage == PaymentErrors.Deposit_error));
        }
        
        //write test to cover this scenario of the MakeDeposit method -  if (payment.ElevyAmount > 0)
        //                {
        //                    var elevyDepositRequest = new Accounting.Sdk.Models.DepositRequest
        //                    {
        //                        AccountNumber = _businessInfoConfig.ElevyPosAccount,
        //                        AccountingType = AccountingType.ReceiveMoney,
        //                        TransactionDate = payment.PaymentDate,
        //                        TransactionAmount = payment.ElevyAmount,
        //                        DateFormat = "dd MMMM yyy",
        //                        Description = payment.Description
        //                    };
        //                    var elevyDepositResponse = await _accountingApi.Deposit(elevyDepositRequest);
        //                    
        //                    _logger.LogDebug("elevy deposit request => {Request} response {Response}.", 
        //                        JsonConvert.SerializeObject(elevyDepositRequest), JsonConvert.SerializeObject(elevyDepositResponse));
        //                }
        [Fact]
        public async Task MakeDeposit_ShouldHandleElevyDeposit_WhenElevyAmountIsGreaterThanZero()
        {
            var payment = new Payment
            {
                AmountPaid = 100, Fee = 10, FineractSavingsAccountId = 1, PaymentType = "TestType", ElevyAmount = 5,
            };
            var settings = new AccountingSettings
            {
                PaymentTypes = new System.Collections.Generic.List<AccountingPaymentType>
                {
                    new AccountingPaymentType { Name = "TestType", Id = 1 }
                },
                ExcludedBusinesses = new System.Collections.Generic.List<string>()
            };

            var businessInfo = new BusinessInfoConfig { ElevyPosAccount = "ELEVY123" };
            var accountingSettings = Options.Create(settings);
            var businessInfoConfig = Options.Create(businessInfo);
            var chargeService = Substitute.For<IChargeService>();
            var glPostingService = Substitute.For<IGlPostingService>();
            var errorProducer = Substitute.For<IErrorProducer>();
            var accountingApi = Substitute.For<IAccountingApi>();

            accountingApi.Deposit(Arg.Any<Accounting.Sdk.Models.DepositRequest>())
                .Returns(Task.FromResult(new Accounting.Sdk.Models.DepositResponse() { Status = "2000" }));

            var logger = Substitute.For<ILogger<DepositService>>();
            var depositService = new DepositService(logger, accountingSettings, chargeService, glPostingService,
                accountingApi, errorProducer, businessInfoConfig);

            var result = await depositService.MakeDeposit(payment);

            Assert.True(result);
            await accountingApi.Received(1).Deposit(Arg.Is<Accounting.Sdk.Models.DepositRequest>(req =>
                req.AccountNumber == businessInfo.ElevyPosAccount &&
                req.TransactionAmount == payment.ElevyAmount));
        }
        
        [Fact]
        public async Task MakeDeposit_ShouldHandleException_WhenAnErrorOccurs()
        {
            var payment = new Payment
            {
                AmountPaid = 100, Fee = 10, FineractSavingsAccountId = 1, PaymentType = "TestType"
            };
            var settings = new AccountingSettings
            {
                PaymentTypes = new System.Collections.Generic.List<AccountingPaymentType>
                {
                    new AccountingPaymentType { Name = "TestType", Id = 1 }
                },
                ExcludedBusinesses = new System.Collections.Generic.List<string>()
            };

            var businessInfo = new BusinessInfoConfig();
            var accountingSettings = Options.Create(settings);
            var businessInfoConfig = Options.Create(businessInfo);
            var chargeService = Substitute.For<IChargeService>();
            var glPostingService = Substitute.For<IGlPostingService>();
            var errorProducer = Substitute.For<IErrorProducer>();
            var accountingApi = Substitute.For<IAccountingApi>();

            accountingApi.Deposit(Arg.Any<Accounting.Sdk.Models.DepositRequest>())
                .Throws(new Exception("Simulated exception"));

            var logger = Substitute.For<ILogger<DepositService>>();
            var depositService = new DepositService(logger, accountingSettings, chargeService, glPostingService,
                accountingApi, errorProducer, businessInfoConfig);

            var result = await depositService.MakeDeposit(payment);

            Assert.False(result);
            logger.Received(1).Log(
                LogLevel.Error,
                Arg.Any<EventId>(),
                Arg.Any<object>(),
                Arg.Any<Exception>(),
                Arg.Any<Func<object, Exception, string>>());       
        }
    }
}

<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netcoreapp3.1</TargetFramework>

        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Akka.TestKit.Xunit2" Version="1.4.51" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="3.1.32" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="3.1.32" />


        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="3.1.32" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="3.1.32" />

        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.7.1" />
        <PackageReference Include="Moq" Version="4.14.7" />
        <PackageReference Include="NSubstitute" Version="5.3.0" />
        <PackageReference Include="xunit" Version="2.4.1" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.4.3">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector" Version="1.3.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\src\Hubtel.Accounting.Data\Hubtel.Accounting.Data.csproj" />
        <ProjectReference Include="..\..\src\Hubtel.Accounting.Processor\Hubtel.Accounting.Processor.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>

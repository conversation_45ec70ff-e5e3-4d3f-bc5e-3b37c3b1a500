using System;
using System.Collections.Generic;
using Gelf.Extensions.Logging;
using Hubtel.Accounting.Data;
using Hubtel.Accounting.Data.Repository;
using Hubtel.Accounting.Processor.Config;
using Hubtel.Accounting.Processor.Models;
using Hubtel.Accounting.Processor.Services.Interfaces;
using Hubtel.Accounting.Processor.Services.ProviderPostings;
using Hubtel.Accounting.Processor.Services.Providers;
using Hubtel.Accounting.Sdk.Extensions;
using Hubtel.Redis.Sdk.Options;
using Hubtel.Redis.Sdk.Services;
using JustEat.StatsD;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NSubstitute;
using StackExchange.Redis;

namespace Hubtel.Accounting.Processor.Tests
{
    public class DiFixture
    {
        public ISalesDataService SalesDataService { get; private set; }
        public IChargeService ChargeService { get; private set; }
        public IDepositService DepositService { get; private set; }
        
        public IDatabase RedisDb { get; private set; }
        public IMultiRedisHostCacheRepository MultiHostRedis { get; private set; }
        // MultiHostRedis = CreateMultiHostRedis(RedisDb);

        public DiFixture()
        {
            var services = new ServiceCollection();
            ConfigurationManager.Init();

            services.AddLogging(loggingBuilder =>
                        loggingBuilder.AddConfiguration(ConfigurationManager.Configuration.GetSection("Logging"))
                            .ClearProviders().SetMinimumLevel(LogLevel.Debug).AddConsole()

                            .AddGelf((c) =>
                            {

                                c.AdditionalFields = new Dictionary<string, object>()
                                {
                                    {"facility", ConfigurationManager.Configuration.GetSection("Logging")["GELF:Facility"]},
                                    {"Environment", ConfigurationManager.Configuration.GetSection("Logging")["GELF:Environment"]},
                                    {"machine_name", Environment.MachineName}
                                };
                                c.Host = ConfigurationManager.Configuration.GetSection("Logging")["GELF:Host"];
                                c.LogSource = ConfigurationManager.Configuration.GetSection("Logging")["GELF:LogSource"];
                                c.Port = int.Parse(ConfigurationManager.Configuration.GetSection("Logging")["GELF:Port"]);

                            }));

            services.AddDbContext<AccountingDbContext>(options =>
                options.UseNpgsql(ConfigurationManager.Configuration.GetSection("DatabaseSettings")["ConnectionString"],
                o => o.MigrationsAssembly("Hubtel.Accounting.Processor")));

            services.AddDbContext<UnifiedSalesContext>(options =>
                options.UseNpgsql(ConfigurationManager.Configuration.GetSection("DatabaseSettings")["SalesReaderConnection"]));

            services.AddHubtelAccountingSdk(options =>
            {
                options.BaseUrl = ConfigurationManager.Configuration.GetSection("AccountingSdkConfig")["BaseUrl"];
                options.ReceiveMoneyUrl = ConfigurationManager.Configuration.GetSection("AccountingSdkConfig")["ReceiveMoneyUrl"];
                options.Token = "";
            });
            
            services.Configure<DatabaseSettings>(ConfigurationManager.Configuration.GetSection(nameof(DatabaseSettings)));
            services.Configure<KafkaSettings>(ConfigurationManager.Configuration.GetSection(nameof(KafkaSettings)));
            services.Configure<AccountingHostedServiceSetting>(ConfigurationManager.Configuration.GetSection(nameof(AccountingHostedServiceSetting)));
            services.Configure<AccountingSettings>(ConfigurationManager.Configuration.GetSection(nameof(AccountingSettings)));

            // Use NSubstitute to create a substitute for ISalesDataService.
            SalesDataService = Substitute.For<ISalesDataService>();
            services.AddSingleton(SalesDataService);

            services.AddSingleton(provider => new GlPostingServiceDependencies
            {
                MtnGhGlPostingService = provider.GetService<IMtnGhGlPostingService>(),
                VodafoneGhGlPostingService = provider.GetService<IVodafoneGhGlPostingService>(),
                TigoGhGlPostingService = provider.GetService<ITigoGhGlPostingService>(),
                AirtelGhGlPostingService = provider.GetService<IAirtelGhGlPostingService>()
            });
            
            //add services
            services.AddScoped<IChargeService, ChargeService>();
            services.AddScoped<IDepositService, DepositService>();
            services.AddScoped<IGlPostingService, GlPostingService>();
            services.AddScoped<IGhqrGlPostingService, GhqrGlPostingService>();

            services.AddScoped<ISalesDataService, SalesDataService>();
            services.AddScoped<IPaymentQueryRepository, EfPaymentQueryRepository>();

           

            services.AddScoped<IAirtelGhGlPostingService, AirtelGhGlPostingService>();
            services.AddScoped<IBarclaysGhGlPostingService, BarclaysGhGlPostingService>();
            services.AddScoped<IEcobankGhGlPostingService, EcobankGhGlPostingService>();
            services.AddScoped<IMtnGhGlPostingService, MtnGhGlPostingService>();
            services.AddScoped<IPayworksZenithGhGlPostingService, PayworksZenithGhGlPostingService>();
            services.AddScoped<ITigoGhGlPostingService, TigoGhGlPostingService>();
            services.AddScoped<IVodafoneGhGlPostingService, VodafoneGhGlPostingService>();
            services.AddScoped<IHubtelGlPostingService, HubtelGlPostingService>();


            services.AddScoped((p) =>
            {
                TelemetryConfiguration configuration = TelemetryConfiguration.CreateDefault();
                configuration.InstrumentationKey = "***";
                return new TelemetryClient(configuration);
            });


            services.AddScoped<IErrorProducer, ErrorProducer>();

         services.AddStatsD((provider) =>
            {
                return new StatsDConfiguration()
                {
                    Host = ConfigurationManager.Configuration.GetSection("StatsD")["Host"],
                    Port = int.Parse(ConfigurationManager.Configuration.GetSection("StatsD")["Port"]),
                    Prefix = "Hubtel_Accounting_Processor",
                    OnError = ex => {
                        var ecp = ex.ToString();
                        return true;
                    }
                };
            });
         
            RedisDb = Substitute.For<IDatabase>();
            
            MultiHostRedis = CreateMultiHostRedis(RedisDb);

            ServiceProvider = services.BuildServiceProvider();
            ChargeService = ServiceProvider.GetService<IChargeService>();
            DepositService = ServiceProvider.GetService<IDepositService>();
        }

        private List<Payment> GetSamplePayments()
        {
            return new List<Payment>();
        }

        public ServiceProvider ServiceProvider { get; private set; }
        
        private static MultiRedisHostCacheRepository CreateMultiHostRedis(IDatabase redisDb)
        {
            return new MultiRedisHostCacheRepository(new Dictionary<string, RedisDbHosts>
            {
                {
                    "host", new RedisDbHosts(new Dictionary<int, IDatabase>
                    {
                        { 0, redisDb },
                    })
                },
                {
                    "payments-received-redis", new RedisDbHosts(new Dictionary<int, IDatabase>()
                    {
                        { 1, redisDb },
                    }) 
                }
            }, new RedisConfiguration
            {
                Setup = new List<RedisHostSetup>
                {
                    new RedisHostSetup
                    {
                        Name = "host",
                        Databases = new List<RedisDbConfig>
                        {
                            new RedisDbConfig
                            {
                                Alias = "db",
                                Db = 0
                            },
                        }
                    },
                    new RedisHostSetup
                    {
                        Name = "payments-received-redis",
                        Databases = new List<RedisDbConfig>
                        {
                            new RedisDbConfig
                            {
                                Alias = "payments-received-db",
                                Db = 1
                            },
                        }
                    }
                }
            });
        }

        
    }
}


{"DatabaseSettings": {"ConnectionString": "Server=localhost;Database=HubtelAccountingDb;UserId=********;Password=********", "SalesReaderConnection": "Server=localhost;Database=HubtelUnifiedSales;UserId=********;Password=********"}, "AccountingHostedServiceSetting": {"TimeInterval": 3600}, "KafkaSettings": {"BootstrapServers": "************:9092,*************:9092,************:9092", "ErrorTopic": "hubtel.accounting_poster.error", "ConsumerGroupId": "Hubtel.ReceiveMoneyAccounting.Poster", "EnableAutoCommit": true, "PollTimeout": 10}, "AccountingSdkConfig": {"BaseUrl": "http://localhost:6578", "ReceiveMoneyUrl": "http://localhost:6578", "AuthToken": ""}, "AllowedHosts": "*", "AccountingSettings": {"BaseUrl": "https://***************:8443/fineract-provider/api/v1/", "Username": "Sales", "Password": "X7K9Zq9ELk", "TenantId": "default", "PaymentTypes": [{"Name": "cash", "Id": 1}, {"Name": "mobilemoney", "Id": 2}, {"Name": "momo", "Id": 2}, {"Name": "card", "Id": 3}, {"Name": "hubtel", "Id": 7}, {"Name": "commission", "Id": 8}], "ChargeIds": [{"Name": "mobilemoney", "Channels": [{"Name": "mtn-gh", "Id": 1}, {"Name": "mtn-gh-cbg", "Id": 1}, {"Name": "vodafone-gh", "Id": 2}, {"Name": "vodafone-gh-ussd", "Id": 2}, {"Name": "airtel-gh", "Id": 4}, {"Name": "tigo-gh", "Id": 3}]}, {"Name": "momo", "Channels": [{"Name": "mtn-gh", "Id": 1}, {"Name": "mtn-gh-cbg", "Id": 1}, {"Name": "vodafone-gh", "Id": 2}, {"Name": "vodafone-gh-ussd", "Id": 2}, {"Name": "airtel-gh", "Id": 4}, {"Name": "tigo-gh", "Id": 3}]}, {"Name": "card", "Channels": [{"Name": "card_present", "Id": 5}, {"Name": "card_not_present", "Id": 6}]}, {"Name": "hubtel", "Channels": [{"Name": "hubtel", "Id": 7}]}]}, "Serilog": {"Using": ["Serilog.Sinks.Graylog", "Serilog.Sinks.Console"], "MinimumLevel": "Verbose", "WriteTo": [{"Name": "Graylog", "Args": {"hostnameOrAddress": "graylog.internal.hubtel.com", "port": "12203", "transportType": "Http"}}, {"Name": "<PERSON><PERSON><PERSON>"}]}, "StatsD": {"Host": "*************", "Port": 8125}, "ApplicationInsights": {"InstrumentationKey": "********-6699-4093-b038-c31d2875695d"}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "GELF": {"Host": "localhost", "Port": 12202, "LogSource": "Hubtel.Accounting.Processor", "Facility": "Hubtel.Accounting.Processor", "Environment": "Production", "LogLevel": {"Default": "Debug"}}}}